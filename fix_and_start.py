#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键修复并启动系统
"""

import os
import sys
import subprocess
import time
import requests
import json

def install_backend_deps():
    """安装后端依赖"""
    print("📦 安装后端依赖...")
    
    # 切换到backend目录
    backend_dir = os.path.join(os.getcwd(), "backend")
    if not os.path.exists(backend_dir):
        print("❌ backend目录不存在")
        return False
    
    try:
        # 安装简化版依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements_simple.txt"
        ], cwd=backend_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 后端依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖时出错: {str(e)}")
        return False

def setup_directories():
    """设置必要的目录"""
    print("📁 设置目录...")
    
    directories = [
        "uploads",
        "uploads/temp", 
        "uploads/heatmaps",
        "uploads/images"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def start_backend():
    """启动后端"""
    print("🚀 启动后端...")
    
    backend_dir = os.path.join(os.getcwd(), "backend")
    
    try:
        # 启动后端服务器（非阻塞）
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd=backend_dir)
        
        print(f"✅ 后端进程已启动，PID: {process.pid}")
        
        # 等待服务器启动
        print("⏳ 等待后端服务器启动...")
        for i in range(10):
            try:
                response = requests.get("http://localhost:8000/", timeout=2)
                if response.status_code == 200:
                    print("✅ 后端服务器启动成功！")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"   等待中... ({i+1}/10)")
        
        print("⚠️  后端可能需要更多时间启动")
        return process
        
    except Exception as e:
        print(f"❌ 启动后端失败: {str(e)}")
        return None

def test_api():
    """测试API"""
    print("🧪 测试API...")
    
    try:
        # 1. 健康检查
        health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ 健康检查失败: {health_response.status_code}")
            return False
        print("✅ 健康检查通过")
        
        # 2. 上传测试
        test_content = b"test image content for quick fix"
        files = {'file': ('test.jpg', test_content, 'image/jpeg')}
        
        upload_response = requests.post(
            "http://localhost:8000/api/v1/detection/upload",
            files=files,
            timeout=10
        )
        
        if upload_response.status_code != 200:
            print(f"❌ 上传测试失败: {upload_response.status_code}")
            print(f"错误: {upload_response.text}")
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data['data']['file_id']
        print(f"✅ 上传测试成功，文件ID: {file_id}")
        
        # 3. 检测结果测试
        time.sleep(1)
        result_response = requests.get(
            f"http://localhost:8000/api/v1/detection/result/{file_id}",
            timeout=10
        )
        
        if result_response.status_code == 200:
            result_data = result_response.json()
            print("✅ 检测结果获取成功！")
            print(f"📊 检测结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 检测结果获取失败: {result_response.status_code}")
            print(f"错误: {result_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    print("🎯 一键修复并启动系统")
    print("=" * 50)
    
    # 1. 设置目录
    setup_directories()
    
    # 2. 安装依赖
    if not install_backend_deps():
        print("❌ 依赖安装失败，无法继续")
        return
    
    # 3. 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败")
        return
    
    # 4. 测试API
    time.sleep(3)  # 给后端更多启动时间
    if test_api():
        print("\n🎉 系统修复并启动成功！")
        print("🌐 前端地址: http://localhost:3001")
        print("🔧 后端地址: http://localhost:8000")
        print("📝 现在可以在前端上传图片进行测试了")
        
        # 保持后端运行
        try:
            print("\n⏳ 后端正在运行，按 Ctrl+C 停止...")
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止后端...")
            backend_process.terminate()
            backend_process.wait()
            print("✅ 后端已停止")
    else:
        print("\n❌ API测试失败，请检查后端日志")
        backend_process.terminate()

if __name__ == "__main__":
    main()
