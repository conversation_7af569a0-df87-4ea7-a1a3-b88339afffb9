#!/bin/bash

echo "开始运行 test_azure.py..."
echo "当前目录: $(pwd)"
echo "Python版本: $(python3 --version)"
echo ""

# 检查文件是否存在
if [ -f "test_azure.py" ]; then
    echo "✅ 找到 test_azure.py 文件"
    echo "文件大小: $(wc -l < test_azure.py) 行"
    echo ""
    
    # 运行Python脚本
    echo "🚀 正在运行 test_azure.py..."
    echo "----------------------------------------"
    python3 test_azure.py
    echo "----------------------------------------"
    echo "✅ 运行完成"
else
    echo "❌ 未找到 test_azure.py 文件"
    echo "当前目录内容:"
    ls -la
fi
