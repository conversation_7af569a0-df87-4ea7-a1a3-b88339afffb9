#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端API测试脚本
用于测试慧眼AI篡改图片检测平台的后端API
"""

import requests
import json
import os
from datetime import datetime

def test_backend_health():
    """测试后端健康状态"""
    print("=" * 60)
    print("🏥 后端健康检查")
    print("=" * 60)
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
            print(f"📡 响应: {response.json()}")
        else:
            print(f"❌ 后端服务器响应异常: {response.status_code}")
            return False
            
        # 测试健康检查端点
        health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ 检测服务健康检查通过")
            print(f"📊 服务状态: {health_response.json()}")
        else:
            print(f"⚠️  检测服务健康检查失败: {health_response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        print("💡 请确保后端服务器正在运行: python3 backend/main.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n" + "=" * 60)
    print("🔗 API端点测试")
    print("=" * 60)
    
    # 测试文件上传（使用README.md作为测试文件）
    test_file_path = "README.md"
    if not os.path.exists(test_file_path):
        print("❌ 测试文件不存在，跳过上传测试")
        return False
    
    try:
        print("📤 测试文件上传...")
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            upload_response = requests.post(
                "http://localhost:8000/api/v1/detection/upload",
                files=files,
                timeout=10
            )
        
        if upload_response.status_code == 200:
            upload_data = upload_response.json()
            print("✅ 文件上传成功")
            print(f"📁 文件ID: {upload_data['data']['file_id']}")
            
            # 测试获取检测结果
            file_id = upload_data['data']['file_id']
            print(f"\n🔍 测试获取检测结果 (ID: {file_id})...")
            
            result_response = requests.get(
                f"http://localhost:8000/api/v1/detection/result/{file_id}",
                timeout=10
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                print("✅ 获取检测结果成功")
                print(f"🎯 检测结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 获取检测结果失败: {result_response.status_code}")
                print(f"📄 响应内容: {result_response.text}")
                return False
        else:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            print(f"📄 响应内容: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_cors():
    """测试CORS配置"""
    print("\n" + "=" * 60)
    print("🌐 CORS配置测试")
    print("=" * 60)
    
    try:
        # 发送OPTIONS请求测试CORS
        response = requests.options(
            "http://localhost:8000/api/v1/detection/health",
            headers={
                'Origin': 'http://localhost:3000',
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=5
        )
        
        if response.status_code in [200, 204]:
            print("✅ CORS配置正常")
            cors_headers = {k: v for k, v in response.headers.items() if k.lower().startswith('access-control')}
            if cors_headers:
                print("🔧 CORS头信息:")
                for header, value in cors_headers.items():
                    print(f"   {header}: {value}")
            return True
        else:
            print(f"⚠️  CORS预检请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CORS测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始后端API测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试后端健康状态
    if not test_backend_health():
        print("\n❌ 后端服务器未运行，请先启动后端服务器")
        print("💡 启动命令: cd backend && python3 main.py")
        return
    
    # 测试API端点
    api_success = test_api_endpoints()
    
    # 测试CORS配置
    cors_success = test_cors()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print(f"✅ 后端服务器: 运行正常")
    print(f"{'✅' if api_success else '❌'} API端点: {'正常' if api_success else '异常'}")
    print(f"{'✅' if cors_success else '❌'} CORS配置: {'正常' if cors_success else '异常'}")
    
    if api_success and cors_success:
        print("\n🎉 所有测试通过！后端API可以正常使用")
        print("💡 现在可以启动前端: cd frontend && npm run dev")
    else:
        print("\n⚠️  部分测试失败，请检查后端配置")

if __name__ == "__main__":
    main()
