# 慧眼AI篡改图片检测平台

## 项目概述

慧眼AI篡改图片检测平台是一个基于人工智能技术的图片篡改检测系统，能够快速、准确地识别图片是否被篡改，并提供详细的篡改区域分析。本平台适用于新闻媒体、司法鉴定、保险理赔等多个领域，为图片真实性鉴别提供专业支持。

## 主要功能

- **AI图片篡改检测**：上传图片，快速检测是否存在篡改痕迹
- **热力图分析**：直观展示可能被篡改的区域及篡改程度
- **检测历史记录**：保存检测历史，方便用户查询和比对
- **用户账户管理**：注册登录、个人信息管理、订阅计划选择
- **多级订阅方案**：免费版、专业版、企业版多种选择

## 技术栈

### 后端
- Python 3.8+
- FastAPI
- SQLAlchemy
- PyTorch (AI模型)

### 前端
- React
- Next.js
- Tailwind CSS
- Axios

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 14+
- npm 6+

### 安装与运行

1. 克隆项目到本地

```bash
git clone https://github.com/your-username/huiyan-ai.git
cd huiyan-ai
```

2. 使用启动脚本一键启动

```bash
./start.sh
```

启动脚本会自动安装所需依赖并启动前后端服务。

3. 手动启动（如果启动脚本不适用）

**后端**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

**前端**
```bash
cd frontend
npm install
npm run dev
```

4. 访问应用

- 前端页面：http://localhost:3000
- 后端API：http://localhost:8000

## 项目结构

```
慧眼AI篡改图片检测平台/
├── backend/             # 后端代码
│   ├── app/            # 应用代码
│   ├── models/         # 数据模型
│   ├── routers/        # API路由
│   ├── services/       # 业务逻辑
│   ├── utils/          # 工具函数
│   ├── main.py         # 入口文件
│   └── requirements.txt # 依赖列表
├── frontend/           # 前端代码
│   ├── public/         # 静态资源
│   ├── src/            # 源代码
│   │   ├── components/ # 组件
│   │   ├── pages/      # 页面
│   │   └── styles/     # 样式
│   ├── package.json    # 依赖配置
│   └── tailwind.config.js # Tailwind配置
└── start.sh            # 启动脚本
```

## API文档

启动后端服务后，可以通过以下地址访问API文档：

- Swagger UI：http://localhost:8000/docs
- ReDoc：http://localhost:8000/redoc

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 详情请参阅 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者：您的名字
- 邮箱：<EMAIL>
- 项目链接：https://github.com/your-username/huiyan-ai

---

© 2023 慧眼AI篡改图片检测平台 版权所有
