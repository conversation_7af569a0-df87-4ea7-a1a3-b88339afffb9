from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status
from sqlalchemy.orm import Session
from typing import Optional

from ...db.session import get_db
from ...core.security import get_current_user
from ...models.user import User
from ...models.detection import Detection
from ...services.ai_detection import detect_image

router = APIRouter()

@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_detection(
    file: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """上传图片进行AI检测"""
    # 检查文件类型
    if not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只接受图片文件"
        )
    
    # 调用AI检测服务
    detection_result = await detect_image(file, current_user)
    
    # 保存检测结果到数据库
    db_detection = Detection(
        user_id=current_user.id,
        result_score=detection_result.get("score"),
        result_label=detection_result.get("label"),
        heatmap_path=detection_result.get("heatmap_path"),
        notes=notes
    )
    db.add(db_detection)
    db.commit()
    db.refresh(db_detection)
    
    return {
        "id": db_detection.id,
        "result_score": db_detection.result_score,
        "result_label": db_detection.result_label,
        "detection_time": db_detection.detection_time,
        "heatmap_available": bool(db_detection.heatmap_path)
    }

@router.get("/")
async def list_detections(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的检测历史列表"""
    detections = db.query(Detection).filter(Detection.user_id == current_user.id).offset(skip).limit(limit).all()
    return detections

@router.get("/{detection_id}")
async def get_detection(
    detection_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取特定检测记录的详细信息"""
    detection = db.query(Detection).filter(Detection.id == detection_id, Detection.user_id == current_user.id).first()
    if not detection:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="检测记录不存在或无权访问"
        )
    return detection
