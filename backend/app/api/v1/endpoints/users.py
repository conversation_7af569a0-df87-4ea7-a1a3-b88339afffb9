from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional

router = APIRouter()

@router.get("/me")
async def get_current_user():
    # 模拟获取当前用户信息
    return {
        "status": "success",
        "message": "获取用户信息成功",
        "data": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin",
            "credits": 100
        }
    }

@router.get("/history")
async def get_detection_history():
    # 模拟获取用户检测历史
    return {
        "status": "success",
        "message": "获取检测历史成功",
        "data": [
            {
                "id": 1,
                "file_id": "sample-file-id-1",
                "filename": "sample1.jpg",
                "detection_time": "2023-01-01T12:00:00",
                "result": {
                    "is_fake": True,
                    "confidence": 0.95
                }
            },
            {
                "id": 2,
                "file_id": "sample-file-id-2",
                "filename": "sample2.jpg",
                "detection_time": "2023-01-02T14:30:00",
                "result": {
                    "is_fake": False,
                    "confidence": 0.85
                }
            }
        ]
    }
