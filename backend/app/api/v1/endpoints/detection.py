from fastapi import APIRouter, UploadFile, File, HTTPException, Response
from fastapi.responses import FileResponse
from typing import Optional
import os
import uuid
from datetime import datetime
import json

router = APIRouter()

# 存储文件ID和文件路径的映射
file_storage = {}
# 存储检测结果
detection_results = {}

@router.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只接受图片文件")

        # 创建上传目录
        upload_dir = os.path.join(os.getcwd(), "uploads", "temp")
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件ID和文件名
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ".jpg"
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存上传的文件
        content = await file.read()
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # 存储文件信息
        file_storage[file_id] = {
            "file_path": file_path,
            "original_filename": file.filename,
            "unique_filename": unique_filename,
            "upload_time": datetime.now().isoformat(),
            "file_size": len(content)
        }

        # 返回成功响应
        return {
            "status": "success",
            "message": "图片上传成功",
            "data": {
                "file_id": file_id,
                "filename": unique_filename,
                "original_filename": file.filename,
                "upload_time": datetime.now().isoformat(),
                "file_path": file_path
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/result/{file_id}")
async def get_detection_result(file_id: str):
    try:
        # 检查文件是否存在
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 如果已经有检测结果，直接返回
        if file_id in detection_results:
            return detection_results[file_id]

        # 进行AI检测
        try:
            from ...services.ai_detection import detect_image
            print("使用完整AI检测服务")
        except ImportError:
            # 如果导入失败，使用简化版本
            from ...services.simple_detection import detect_image
            print("警告: 使用简化检测服务")

        file_info = file_storage[file_id]
        file_path = file_info["file_path"]

        # 创建模拟用户对象
        class MockUser:
            def __init__(self):
                self.id = 1
                self.is_premium = True

        mock_user = MockUser()

        # 创建模拟UploadFile对象
        class MockUploadFile:
            def __init__(self, file_path, filename):
                self.file_path = file_path
                self.filename = filename

            async def read(self):
                with open(self.file_path, 'rb') as f:
                    return f.read()

        mock_file = MockUploadFile(file_path, file_info["original_filename"])

        # 调用AI检测服务
        try:
            detection_result = await detect_image(mock_file, mock_user)
        except Exception as detection_error:
            print(f"AI检测失败: {str(detection_error)}")
            # 使用模拟检测结果
            import random
            detection_result = {
                "is_tampered": random.choice([True, False]),
                "confidence": random.uniform(0.6, 0.95),
                "label": "模拟检测结果",
                "detected_categories": [],
                "heatmap_path": None
            }

        # 构建响应
        result = {
            "status": "success",
            "message": "获取检测结果成功",
            "data": {
                "file_id": file_id,
                "is_fake": detection_result.get("is_tampered", False),
                "confidence": detection_result.get("confidence", 0.5),
                "detection_time": datetime.now().isoformat(),
                "heatmap_url": f"/api/v1/detection/heatmap/{file_id}" if detection_result.get("heatmap_path") else None,
                "label": detection_result.get("label", "未知"),
                "detected_categories": detection_result.get("detected_categories", [])
            }
        }

        # 缓存检测结果
        detection_results[file_id] = result

        return result

    except HTTPException:
        raise
    except Exception as e:
        print(f"检测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取检测结果失败: {str(e)}")

@router.get("/image/{file_id}")
async def get_image(file_id: str):
    """获取上传的原始图片"""
    try:
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="图片不存在")

        file_info = file_storage[file_id]
        file_path = file_info["file_path"]

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="图片文件不存在")

        return FileResponse(
            file_path,
            media_type="image/jpeg",
            filename=file_info["original_filename"]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")

@router.get("/heatmap/{file_id}")
async def get_heatmap(file_id: str):
    """获取检测热力图"""
    try:
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 确保已经进行了检测
        if file_id not in detection_results:
            # 如果没有检测结果，先进行检测
            await get_detection_result(file_id)

        # 查找热力图文件
        heatmap_path = None

        # 检查uploads/heatmaps目录中的热力图文件
        heatmap_dir = os.path.join(os.getcwd(), "uploads", "heatmaps")
        if os.path.exists(heatmap_dir):
            for filename in os.listdir(heatmap_dir):
                if filename.startswith("heatmap_") and filename.endswith(".png"):
                    # 使用最新的热力图文件
                    potential_path = os.path.join(heatmap_dir, filename)
                    if not heatmap_path or os.path.getmtime(potential_path) > os.path.getmtime(heatmap_path):
                        heatmap_path = potential_path

        if heatmap_path and os.path.exists(heatmap_path):
            return FileResponse(
                heatmap_path,
                media_type="image/png",
                filename=f"heatmap_{file_id}.png"
            )
        else:
            # 如果没有热力图，返回原图
            file_info = file_storage[file_id]
            file_path = file_info["file_path"]

            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="图片文件不存在")

            return FileResponse(
                file_path,
                media_type="image/jpeg",
                filename=f"original_{file_info['original_filename']}"
            )

    except HTTPException:
        raise
    except Exception as e:
        print(f"获取热力图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取热力图失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "检测服务运行正常",
        "timestamp": datetime.now().isoformat(),
        "uploaded_files": len(file_storage)
    }
