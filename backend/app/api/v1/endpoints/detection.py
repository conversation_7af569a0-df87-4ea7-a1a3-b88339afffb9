from fastapi import APIRouter, UploadFile, File, HTTPException, Response
from fastapi.responses import FileResponse
from typing import Optional
import os
import uuid
from datetime import datetime
import json

router = APIRouter()

# 存储文件ID和文件路径的映射
file_storage = {}

@router.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只接受图片文件")

        # 创建上传目录
        upload_dir = os.path.join(os.getcwd(), "uploads", "temp")
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件ID和文件名
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ".jpg"
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存上传的文件
        content = await file.read()
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # 存储文件信息
        file_storage[file_id] = {
            "file_path": file_path,
            "original_filename": file.filename,
            "unique_filename": unique_filename,
            "upload_time": datetime.now().isoformat(),
            "file_size": len(content)
        }

        # 返回成功响应
        return {
            "status": "success",
            "message": "图片上传成功",
            "data": {
                "file_id": file_id,
                "filename": unique_filename,
                "original_filename": file.filename,
                "upload_time": datetime.now().isoformat(),
                "file_path": file_path
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/result/{file_id}")
async def get_detection_result(file_id: str):
    try:
        # 检查文件是否存在
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 模拟AI检测过程（这里可以集成真实的AI检测服务）
        import random
        is_fake = random.choice([True, False])
        confidence = random.uniform(0.7, 0.99)

        # 返回检测结果
        return {
            "status": "success",
            "message": "获取检测结果成功",
            "data": {
                "file_id": file_id,
                "is_fake": is_fake,
                "confidence": confidence,
                "detection_time": datetime.now().isoformat(),
                "heatmap_url": f"/api/v1/detection/heatmap/{file_id}" if is_fake else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取检测结果失败: {str(e)}")

@router.get("/image/{file_id}")
async def get_image(file_id: str):
    """获取上传的原始图片"""
    try:
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="图片不存在")

        file_info = file_storage[file_id]
        file_path = file_info["file_path"]

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="图片文件不存在")

        return FileResponse(
            file_path,
            media_type="image/jpeg",
            filename=file_info["original_filename"]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")

@router.get("/heatmap/{file_id}")
async def get_heatmap(file_id: str):
    """获取检测热力图（模拟）"""
    try:
        if file_id not in file_storage:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 这里应该返回真实的热力图，现在返回原图作为示例
        file_info = file_storage[file_id]
        file_path = file_info["file_path"]

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="热力图不存在")

        return FileResponse(
            file_path,
            media_type="image/jpeg",
            filename=f"heatmap_{file_info['original_filename']}"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热力图失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "检测服务运行正常",
        "timestamp": datetime.now().isoformat(),
        "uploaded_files": len(file_storage)
    }
