from fastapi import APIRouter, HTTPException, Depends
from typing import Optional

router = APIRouter()

@router.post("/login")
async def login(username: str, password: str):
    # 模拟登录逻辑
    if username == "admin" and password == "password":
        return {
            "status": "success",
            "message": "登录成功",
            "data": {
                "token": "sample_token_123",
                "user": {
                    "id": 1,
                    "username": username,
                    "role": "admin"
                }
            }
        }
    raise HTTPException(status_code=401, detail="用户名或密码错误")

@router.post("/register")
async def register(username: str, password: str, email: Optional[str] = None):
    # 模拟注册逻辑
    return {
        "status": "success",
        "message": "注册成功",
        "data": {
            "user_id": 1,
            "username": username,
            "email": email
        }
    }
