from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..db.base_class import Base

class Detection(Base):
    __tablename__ = "detections"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    image_path = Column(String, nullable=True)  # 临时存储路径，检测后删除
    result_score = Column(Float, nullable=True)  # AI检测得分
    result_label = Column(String, nullable=True)  # 检测结果标签
    heatmap_path = Column(String, nullable=True)  # 热力图路径（付费功能）
    detection_time = Column(DateTime(timezone=True), server_default=func.now())
    notes = Column(Text, nullable=True)  # 用户备注
    
    # 关系
    user = relationship("User", back_populates="detections")
