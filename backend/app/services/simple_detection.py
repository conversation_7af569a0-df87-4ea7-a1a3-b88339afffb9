#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版AI检测服务
用于在复杂AI服务不可用时提供基本功能
"""

import random
import os
import uuid
from datetime import datetime
from typing import Dict, Any

async def simple_detect_image(file, user) -> Dict[str, Any]:
    """
    简化版图片检测
    
    Args:
        file: 上传的文件对象
        user: 用户对象
        
    Returns:
        检测结果字典
    """
    try:
        # 读取文件内容
        file_content = await file.read()
        file_size = len(file_content)
        
        print(f"简化检测 - 文件大小: {file_size} 字节")
        
        # 基于文件特征进行简单判断
        confidence = calculate_simple_confidence(file_content, file.filename)
        is_tampered = confidence > 0.5
        
        # 生成简单的热力图（如果需要）
        heatmap_path = None
        if is_tampered and confidence > 0.6:
            heatmap_path = generate_simple_heatmap_placeholder(file.filename)
        
        result = {
            "score": confidence,
            "is_tampered": is_tampered,
            "confidence": confidence,
            "label": "疑似AI生成/篡改" if is_tampered else "未检测到明显篡改",
            "detected_categories": generate_mock_categories(confidence),
            "heatmap_path": heatmap_path,
            "detection_method": "简化检测算法"
        }
        
        print(f"简化检测结果: {result}")
        return result
        
    except Exception as e:
        print(f"简化检测失败: {str(e)}")
        # 返回默认结果
        return {
            "score": 0.3,
            "is_tampered": False,
            "confidence": 0.3,
            "label": "检测失败，使用默认结果",
            "detected_categories": [],
            "heatmap_path": None,
            "detection_method": "默认结果"
        }

def calculate_simple_confidence(file_content: bytes, filename: str) -> float:
    """
    基于简单特征计算置信度
    """
    confidence = 0.0
    
    # 基于文件大小
    file_size = len(file_content)
    if file_size > 1024 * 1024:  # 大于1MB
        confidence += 0.2
    elif file_size < 1024:  # 小于1KB
        confidence += 0.4
    
    # 基于文件名
    suspicious_keywords = ['fake', 'ai', 'generated', 'synthetic', 'deepfake']
    filename_lower = filename.lower()
    for keyword in suspicious_keywords:
        if keyword in filename_lower:
            confidence += 0.3
            break
    
    # 基于文件内容的简单特征
    if file_content:
        # 检查文件头
        if file_content.startswith(b'\xff\xd8\xff'):  # JPEG
            confidence += 0.1
        elif file_content.startswith(b'\x89PNG'):  # PNG
            confidence += 0.1
        
        # 简单的熵计算
        unique_bytes = len(set(file_content[:1024]))  # 只检查前1KB
        if unique_bytes < 50:  # 低熵，可能是简单图像
            confidence += 0.2
    
    # 添加随机因素模拟AI检测的不确定性
    confidence += random.uniform(-0.1, 0.3)
    
    # 确保在0-1范围内
    return max(0.0, min(1.0, confidence))

def generate_mock_categories(confidence: float) -> list:
    """生成模拟的检测类别"""
    categories = []
    
    if confidence > 0.7:
        categories.append({
            "category": "AI生成内容",
            "severity": 3
        })
    
    if confidence > 0.5:
        categories.append({
            "category": "图像篡改",
            "severity": 2
        })
    
    if confidence > 0.3:
        categories.append({
            "category": "可疑修改",
            "severity": 1
        })
    
    return categories

def generate_simple_heatmap_placeholder(filename: str) -> str:
    """
    生成简单的热力图占位符路径
    """
    try:
        # 创建热力图目录
        heatmap_dir = os.path.join(os.getcwd(), "uploads", "heatmaps")
        os.makedirs(heatmap_dir, exist_ok=True)
        
        # 生成占位符文件名
        heatmap_filename = f"simple_heatmap_{uuid.uuid4().hex}.txt"
        heatmap_path = os.path.join(heatmap_dir, heatmap_filename)
        
        # 创建占位符文件
        with open(heatmap_path, 'w') as f:
            f.write(f"简化热力图占位符\n")
            f.write(f"原始文件: {filename}\n")
            f.write(f"生成时间: {datetime.now().isoformat()}\n")
            f.write(f"注意: 这是一个占位符，真实热力图需要完整的AI服务\n")
        
        return heatmap_path
        
    except Exception as e:
        print(f"生成热力图占位符失败: {str(e)}")
        return None

# 为了兼容性，提供与原始模块相同的接口
async def detect_image(file, user):
    """兼容接口"""
    return await simple_detect_image(file, user)
