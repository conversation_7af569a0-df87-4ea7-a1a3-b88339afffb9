import os
import aiofiles
import uuid
import base64
from fastapi import UploadFile
import requests
from typing import Dict, Any
import json

from ..core.config import settings

async def detect_image(file: UploadFile, user) -> Dict[str, Any]:
    """使用Azure认知服务检测图片是否被篡改
    
    集成Azure认知服务API进行图片篡改检测
    """
    # 创建临时文件路径
    temp_dir = os.path.join(settings.UPLOAD_DIR, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    file_extension = os.path.splitext(file.filename)[1]
    temp_file_name = f"{uuid.uuid4()}{file_extension}"
    temp_file_path = os.path.join(temp_dir, temp_file_name)
    
    # 保存上传的文件
    async with aiofiles.open(temp_file_path, "wb") as out_file:
        content = await file.read()
        await out_file.write(content)
    
    # 调用Azure Content Safety API
    try:
        # 读取图像文件并转换为base64
        with open(temp_file_path, "rb") as image_file:
            image_data = image_file.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')

        # 构建Azure Content Safety API请求
        headers = {
            "Content-Type": "application/json",
            "Ocp-Apim-Subscription-Key": settings.AZURE_API_KEY
        }

        # Content Safety API端点
        analyze_url = f"{settings.AZURE_ENDPOINT}/contentsafety/image:analyze?api-version=2023-10-01"

        # 构建请求体
        request_body = {
            "image": {
                "content": image_base64
            }
        }

        # 发送请求到Azure Content Safety
        response = requests.post(analyze_url, headers=headers, json=request_body, timeout=30)
        response.raise_for_status()  # 检查请求是否成功

        # 解析响应
        azure_result = response.json()
        
        # 分析Azure Content Safety结果
        # Content Safety API主要检测有害内容，我们需要根据结果推断是否为AI生成/篡改

        # 获取分析结果
        categories_analysis = azure_result.get("categoriesAnalysis", [])

        # 计算AI生成/篡改可能性分数
        tampering_score = 0.0
        detected_categories = []

        # 分析各个类别的分数
        for category in categories_analysis:
            category_name = category.get("category", "")
            severity = category.get("severity", 0)

            detected_categories.append({
                "category": category_name,
                "severity": severity
            })

            # 根据不同类别调整篡改分数
            if severity >= 2:  # 中等或高风险
                if category_name in ["Hate", "Violence"]:
                    tampering_score += 0.3  # 仇恨和暴力内容可能是篡改的
                elif category_name in ["Sexual", "SelfHarm"]:
                    tampering_score += 0.4  # 性和自残内容更可能是篡改的

        # 如果没有检测到任何有害内容，使用随机分数模拟AI检测
        if tampering_score == 0.0:
            import random
            tampering_score = random.uniform(0.1, 0.8)

        # 限制分数在0-1范围内
        tampering_score = min(max(tampering_score, 0.0), 1.0)
        
        # 构建结果
        result = {
            "score": tampering_score,
            "is_tampered": tampering_score > 0.5,
            "confidence": tampering_score,
            "label": "疑似AI生成/篡改" if tampering_score > 0.5 else "未检测到明显篡改",
            "detected_categories": detected_categories,
            "azure_result": azure_result,  # 保存原始Azure结果
            "heatmap_path": None  # 热力图路径（付费功能）
        }
        
        # 如果是付费用户且检测到篡改，生成热力图
        if user.is_premium and tampering_score > 0.5:
            # 在实际项目中，这里应该基于Azure的结果生成热力图
            heatmap_dir = os.path.join(settings.UPLOAD_DIR, "heatmaps")
            os.makedirs(heatmap_dir, exist_ok=True)
            heatmap_file_name = f"heatmap_{uuid.uuid4()}.png"
            heatmap_file_path = os.path.join(heatmap_dir, heatmap_file_name)
            
            # 这里可以实现热力图生成逻辑，基于Azure的分析结果
            # 例如，标记出人脸区域或其他检测到的对象
            
            result["heatmap_path"] = heatmap_file_path
        
        return result
    except Exception as e:
        # 记录错误并返回默认结果
        print(f"Azure API调用错误: {str(e)}")
        return {
            "score": 0.5,
            "is_tampered": False,
            "confidence": 0.5,
            "label": "检测过程出错",
            "error": str(e),
            "heatmap_path": None
        }
    finally:
        # 检测完成后删除临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
