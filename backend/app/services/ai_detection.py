import os
import aiofiles
import uuid
from fastapi import UploadFile
import requests
from typing import Dict, Any
import json

from ..core.config import settings

async def detect_image(file: UploadFile, user) -> Dict[str, Any]:
    """使用Azure认知服务检测图片是否被篡改
    
    集成Azure认知服务API进行图片篡改检测
    """
    # 创建临时文件路径
    temp_dir = os.path.join(settings.UPLOAD_DIR, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    file_extension = os.path.splitext(file.filename)[1]
    temp_file_name = f"{uuid.uuid4()}{file_extension}"
    temp_file_path = os.path.join(temp_dir, temp_file_name)
    
    # 保存上传的文件
    async with aiofiles.open(temp_file_path, "wb") as out_file:
        content = await file.read()
        await out_file.write(content)
    
    # 调用Azure认知服务API
    try:
        # 构建Azure API请求
        headers = {
            "Content-Type": "application/octet-stream",
            "Ocp-Apim-Subscription-Key": settings.AZURE_API_KEY
        }
        
        # 图像分析API端点
        analyze_url = f"{settings.AZURE_ENDPOINT}vision/v3.2/analyze?visualFeatures=Adult,Objects,Faces"
        
        # 读取图像文件
        with open(temp_file_path, "rb") as image_file:
            image_data = image_file.read()
        
        # 发送请求到Azure
        response = requests.post(analyze_url, headers=headers, data=image_data)
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析响应
        azure_result = response.json()
        
        # 分析结果，判断是否篡改
        # 这里使用一个简单的启发式方法：
        # 1. 检查成人内容分数 - 可能表示不适当的修改
        # 2. 检查人脸检测 - 可以用于识别人脸篡改
        adult_score = azure_result.get("adult", {}).get("adultScore", 0)
        is_artificial = azure_result.get("adult", {}).get("isArtificialAdultContent", False)
        faces = azure_result.get("faces", [])
        
        # 计算篡改可能性分数
        tampering_score = 0.0
        
        # 如果检测到人工生成的成人内容，这很可能是篡改
        if is_artificial:
            tampering_score += 0.7
        
        # 根据成人内容分数增加篡改可能性
        tampering_score += adult_score * 0.3
        
        # 如果检测到多个人脸，可能是合成图像
        if len(faces) > 1:
            tampering_score += 0.2
        
        # 限制分数在0-1范围内
        tampering_score = min(max(tampering_score, 0.0), 1.0)
        
        # 构建结果
        result = {
            "score": tampering_score,
            "is_tampered": tampering_score > 0.5,
            "confidence": tampering_score,
            "label": "疑似AI修改" if tampering_score > 0.5 else "未检测到篡改",
            "azure_result": azure_result,  # 保存原始Azure结果
            "heatmap_path": None  # 热力图路径（付费功能）
        }
        
        # 如果是付费用户且检测到篡改，生成热力图
        if user.is_premium and tampering_score > 0.5:
            # 在实际项目中，这里应该基于Azure的结果生成热力图
            heatmap_dir = os.path.join(settings.UPLOAD_DIR, "heatmaps")
            os.makedirs(heatmap_dir, exist_ok=True)
            heatmap_file_name = f"heatmap_{uuid.uuid4()}.png"
            heatmap_file_path = os.path.join(heatmap_dir, heatmap_file_name)
            
            # 这里可以实现热力图生成逻辑，基于Azure的分析结果
            # 例如，标记出人脸区域或其他检测到的对象
            
            result["heatmap_path"] = heatmap_file_path
        
        return result
    except Exception as e:
        # 记录错误并返回默认结果
        print(f"Azure API调用错误: {str(e)}")
        return {
            "score": 0.5,
            "is_tampered": False,
            "confidence": 0.5,
            "label": "检测过程出错",
            "error": str(e),
            "heatmap_path": None
        }
    finally:
        # 检测完成后删除临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
