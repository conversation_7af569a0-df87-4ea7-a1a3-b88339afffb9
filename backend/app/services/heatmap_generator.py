#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热力图生成服务
用于生成AI篡改检测的热力图可视化
"""

import os
import uuid
import numpy as np
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import random
from typing import Tuple, List, Optional
import json

class HeatmapGenerator:
    """热力图生成器"""
    
    def __init__(self):
        self.colors = {
            'low_risk': (0, 255, 0, 100),      # 绿色，低风险
            'medium_risk': (255, 255, 0, 150), # 黄色，中等风险
            'high_risk': (255, 0, 0, 200),     # 红色，高风险
            'very_high_risk': (139, 0, 0, 250) # 深红色，极高风险
        }
    
    def generate_heatmap(self, 
                        original_image_path: str, 
                        detection_result: dict,
                        output_dir: str) -> str:
        """
        生成热力图
        
        Args:
            original_image_path: 原始图片路径
            detection_result: 检测结果
            output_dir: 输出目录
            
        Returns:
            生成的热力图文件路径
        """
        try:
            # 打开原始图片
            original_image = Image.open(original_image_path)
            width, height = original_image.size
            
            # 创建热力图覆盖层
            heatmap_overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(heatmap_overlay)
            
            # 根据检测结果生成热力图区域
            confidence = detection_result.get('confidence', 0.5)
            is_tampered = detection_result.get('is_tampered', False)
            
            if is_tampered and confidence > 0.5:
                # 生成可疑区域
                suspicious_regions = self._generate_suspicious_regions(
                    width, height, confidence
                )
                
                # 绘制热力图区域
                for region in suspicious_regions:
                    self._draw_heatmap_region(draw, region)
            
            # 将热力图覆盖到原图上
            result_image = Image.alpha_composite(
                original_image.convert('RGBA'), 
                heatmap_overlay
            )
            
            # 添加热力图图例
            result_image = self._add_legend(result_image)
            
            # 保存热力图
            heatmap_filename = f"heatmap_{uuid.uuid4().hex}.png"
            heatmap_path = os.path.join(output_dir, heatmap_filename)
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存为PNG格式以保持透明度
            result_image.save(heatmap_path, 'PNG')
            
            return heatmap_path
            
        except Exception as e:
            print(f"生成热力图失败: {str(e)}")
            # 如果生成失败，返回原图路径
            return original_image_path
    
    def _generate_suspicious_regions(self, 
                                   width: int, 
                                   height: int, 
                                   confidence: float) -> List[dict]:
        """生成可疑区域"""
        regions = []
        
        # 根据置信度确定区域数量
        num_regions = max(1, int(confidence * 5))
        
        for i in range(num_regions):
            # 随机生成区域位置和大小
            region_width = random.randint(width // 10, width // 3)
            region_height = random.randint(height // 10, height // 3)
            
            x = random.randint(0, max(1, width - region_width))
            y = random.randint(0, max(1, height - region_height))
            
            # 根据置信度确定风险级别
            risk_level = self._calculate_risk_level(confidence, i, num_regions)
            
            regions.append({
                'bbox': (x, y, x + region_width, y + region_height),
                'risk_level': risk_level,
                'confidence': confidence * random.uniform(0.8, 1.2)
            })
        
        return regions
    
    def _calculate_risk_level(self, 
                            overall_confidence: float, 
                            region_index: int, 
                            total_regions: int) -> str:
        """计算风险级别"""
        # 第一个区域通常是最可疑的
        if region_index == 0:
            if overall_confidence > 0.8:
                return 'very_high_risk'
            elif overall_confidence > 0.6:
                return 'high_risk'
            else:
                return 'medium_risk'
        else:
            # 其他区域风险递减
            if overall_confidence > 0.7:
                return 'high_risk'
            elif overall_confidence > 0.5:
                return 'medium_risk'
            else:
                return 'low_risk'
    
    def _draw_heatmap_region(self, draw: ImageDraw.Draw, region: dict):
        """绘制热力图区域"""
        bbox = region['bbox']
        risk_level = region['risk_level']
        color = self.colors[risk_level]
        
        # 绘制半透明矩形
        draw.rectangle(bbox, fill=color, outline=None)
        
        # 添加边框
        border_color = (color[0], color[1], color[2], 255)
        draw.rectangle(bbox, outline=border_color, width=2)
    
    def _add_legend(self, image: Image.Image) -> Image.Image:
        """添加热力图图例"""
        try:
            # 创建图例
            legend_width = 200
            legend_height = 120
            legend = Image.new('RGBA', (legend_width, legend_height), (255, 255, 255, 200))
            legend_draw = ImageDraw.Draw(legend)
            
            # 绘制图例标题
            legend_draw.text((10, 5), "风险级别:", fill=(0, 0, 0, 255))
            
            # 绘制颜色块和标签
            y_offset = 25
            legend_items = [
                ('极高风险', self.colors['very_high_risk']),
                ('高风险', self.colors['high_risk']),
                ('中等风险', self.colors['medium_risk']),
                ('低风险', self.colors['low_risk'])
            ]
            
            for label, color in legend_items:
                # 绘制颜色块
                legend_draw.rectangle(
                    (10, y_offset, 30, y_offset + 15), 
                    fill=color, 
                    outline=(0, 0, 0, 255)
                )
                # 绘制标签
                legend_draw.text((35, y_offset), label, fill=(0, 0, 0, 255))
                y_offset += 20
            
            # 将图例粘贴到图片右上角
            image_width, image_height = image.size
            legend_x = image_width - legend_width - 10
            legend_y = 10
            
            image.paste(legend, (legend_x, legend_y), legend)
            
            return image
            
        except Exception as e:
            print(f"添加图例失败: {str(e)}")
            return image

def generate_ai_detection_heatmap(image_path: str, 
                                detection_result: dict, 
                                output_dir: str) -> str:
    """
    生成AI检测热力图的便捷函数
    
    Args:
        image_path: 原始图片路径
        detection_result: 检测结果字典
        output_dir: 输出目录
        
    Returns:
        热力图文件路径
    """
    generator = HeatmapGenerator()
    return generator.generate_heatmap(image_path, detection_result, output_dir)
