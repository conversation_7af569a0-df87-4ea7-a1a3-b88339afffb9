#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版热力图生成器
只使用PIL库生成热力图，不依赖numpy和opencv
"""

import os
import uuid
import random
from PIL import Image, ImageDraw, ImageFont
from typing import List, <PERSON><PERSON>

def generate_simple_heatmap(original_image_path: str, 
                          detection_result: dict, 
                          output_dir: str) -> str:
    """
    生成简化版热力图
    
    Args:
        original_image_path: 原始图片路径
        detection_result: 检测结果
        output_dir: 输出目录
        
    Returns:
        生成的热力图文件路径
    """
    try:
        # 打开原始图片
        original_image = Image.open(original_image_path)
        width, height = original_image.size
        
        # 转换为RGBA模式以支持透明度
        if original_image.mode != 'RGBA':
            original_image = original_image.convert('RGBA')
        
        # 创建热力图覆盖层
        overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # 获取检测结果
        confidence = detection_result.get('confidence', 0.5)
        is_tampered = detection_result.get('is_tampered', False)
        
        print(f"生成热力图 - 置信度: {confidence}, 是否篡改: {is_tampered}")
        
        if is_tampered and confidence > 0.3:
            # 生成可疑区域
            suspicious_regions = generate_suspicious_regions(width, height, confidence)
            
            # 绘制热力图区域
            for region in suspicious_regions:
                draw_heatmap_region(draw, region)
            
            # 添加图例
            add_simple_legend(draw, width, height)
        
        # 合成最终图像
        result_image = Image.alpha_composite(original_image, overlay)
        
        # 保存热力图
        os.makedirs(output_dir, exist_ok=True)
        heatmap_filename = f"heatmap_{uuid.uuid4().hex}.png"
        heatmap_path = os.path.join(output_dir, heatmap_filename)
        
        result_image.save(heatmap_path, 'PNG')
        print(f"热力图已保存: {heatmap_path}")
        
        return heatmap_path
        
    except Exception as e:
        print(f"生成简化热力图失败: {str(e)}")
        return original_image_path

def generate_suspicious_regions(width: int, height: int, confidence: float) -> List[dict]:
    """生成可疑区域"""
    regions = []
    
    # 根据置信度确定区域数量
    num_regions = max(1, min(5, int(confidence * 6)))
    
    for i in range(num_regions):
        # 生成区域大小（相对于图片大小）
        min_size = min(width, height) // 15
        max_size = min(width, height) // 4
        
        region_width = random.randint(min_size, max_size)
        region_height = random.randint(min_size, max_size)
        
        # 生成区域位置
        x = random.randint(0, max(1, width - region_width))
        y = random.randint(0, max(1, height - region_height))
        
        # 确定风险级别和颜色
        if i == 0:  # 第一个区域最可疑
            if confidence > 0.8:
                risk_color = (255, 0, 0, 180)  # 深红色
                risk_level = "极高风险"
            elif confidence > 0.6:
                risk_color = (255, 100, 0, 150)  # 橙红色
                risk_level = "高风险"
            else:
                risk_color = (255, 255, 0, 120)  # 黄色
                risk_level = "中等风险"
        else:
            # 其他区域风险递减
            alpha = max(80, 180 - i * 30)
            if confidence > 0.7:
                risk_color = (255, 150, 0, alpha)  # 橙色
                risk_level = "中等风险"
            else:
                risk_color = (255, 255, 0, alpha)  # 黄色
                risk_level = "低风险"
        
        regions.append({
            'bbox': (x, y, x + region_width, y + region_height),
            'color': risk_color,
            'risk_level': risk_level
        })
    
    return regions

def draw_heatmap_region(draw: ImageDraw.Draw, region: dict):
    """绘制热力图区域"""
    bbox = region['bbox']
    color = region['color']
    
    # 绘制半透明填充矩形
    draw.rectangle(bbox, fill=color)
    
    # 绘制边框
    border_color = (color[0], color[1], color[2], 255)
    draw.rectangle(bbox, outline=border_color, width=3)
    
    # 在区域中心添加警告标记
    center_x = (bbox[0] + bbox[2]) // 2
    center_y = (bbox[1] + bbox[3]) // 2
    
    # 绘制简单的警告三角形
    triangle_size = min(bbox[2] - bbox[0], bbox[3] - bbox[1]) // 4
    triangle_points = [
        (center_x, center_y - triangle_size),
        (center_x - triangle_size, center_y + triangle_size),
        (center_x + triangle_size, center_y + triangle_size)
    ]
    draw.polygon(triangle_points, fill=(255, 255, 255, 200), outline=(0, 0, 0, 255))
    
    # 在三角形中心添加感叹号
    try:
        # 尝试使用默认字体
        draw.text((center_x-3, center_y-5), "!", fill=(0, 0, 0, 255))
    except:
        # 如果字体加载失败，绘制一个简单的点
        draw.ellipse((center_x-2, center_y-2, center_x+2, center_y+2), fill=(0, 0, 0, 255))

def add_simple_legend(draw: ImageDraw.Draw, width: int, height: int):
    """添加简单图例"""
    try:
        # 图例位置（右上角）
        legend_x = width - 180
        legend_y = 10
        legend_width = 170
        legend_height = 100
        
        # 绘制图例背景
        legend_bg = (legend_x, legend_y, legend_x + legend_width, legend_y + legend_height)
        draw.rectangle(legend_bg, fill=(255, 255, 255, 200), outline=(0, 0, 0, 255))
        
        # 图例标题
        draw.text((legend_x + 5, legend_y + 5), "AI篡改检测", fill=(0, 0, 0, 255))
        
        # 颜色说明
        legend_items = [
            ("极高风险", (255, 0, 0, 180)),
            ("高风险", (255, 100, 0, 150)),
            ("中等风险", (255, 255, 0, 120)),
            ("低风险", (255, 255, 0, 80))
        ]
        
        y_offset = legend_y + 25
        for label, color in legend_items:
            # 绘制颜色块
            color_rect = (legend_x + 5, y_offset, legend_x + 20, y_offset + 12)
            draw.rectangle(color_rect, fill=color, outline=(0, 0, 0, 255))
            
            # 绘制标签
            draw.text((legend_x + 25, y_offset), label, fill=(0, 0, 0, 255))
            y_offset += 15
            
    except Exception as e:
        print(f"添加图例失败: {str(e)}")

# 为了兼容性，提供与原始模块相同的接口
def generate_ai_detection_heatmap(image_path: str, 
                                detection_result: dict, 
                                output_dir: str) -> str:
    """
    生成AI检测热力图的便捷函数（简化版）
    """
    return generate_simple_heatmap(image_path, detection_result, output_dir)
