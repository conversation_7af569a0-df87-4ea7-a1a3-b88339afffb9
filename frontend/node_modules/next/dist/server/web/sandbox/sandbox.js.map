{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "params", "then", "result", "waitUntil", "catch", "error", "getServerError", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "requestToBodyStream", "headerName", "response", "headers", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAQaA,WAAW;eAAXA;;IAuCSC,iBAAiB;eAAjBA;;IA2BTC,GAAG;eAAHA;;;4BAvEkB;yBACE;6BACG;kCACC;AAE9B,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAaD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,OAAO,CAACC,SACND,GAAGC,QACAC,IAAI,CAAC,CAACC;gBAEMA;mBAFM;gBACjB,GAAGA,MAAM;gBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;oBACnC,mGAAmG;oBACnG,MAAMC,IAAAA,0BAAc,EAACD,OAAO;gBAC9B;YACF;WACCD,KAAK,CAAC,CAACC;YACN,+CAA+C;YAC/C,MAAMC,IAAAA,0BAAc,EAACD,OAAO;QAC9B;AACN;AAEO,eAAeX,kBAAkBM,MAQvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYV,OAAOW,IAAI;QACvBC,WAAWZ,OAAOY,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAapB,OAAOqB,KAAK,CAAE;QACpCb,kBAAkBY;IACpB;IACA,OAAOb;AACT;AAEO,MAAMZ,MAAMG,iBAAiB,eAAewB,oBAAoBtB,MAAM;QAQvEA;IAPJ,MAAMO,UAAU,MAAMb,kBAAkBM;IACxC,MAAMuB,eAGJhB,QAAQU,OAAO,CAACO,QAAQ,CAAC,CAAC,WAAW,EAAExB,OAAOW,IAAI,CAAC,CAAC,CAAC,CAACc,OAAO;IAE/D,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC3B,OAAO4B,OAAO,CAACC,MAAM,KAC1D7B,uBAAAA,OAAO4B,OAAO,CAACE,IAAI,qBAAnB9B,qBAAqB+B,eAAe,KACpCC;IAEJ,MAAMC,cAAc1B,QAAQ2B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIpC,OAAO4B,OAAO,CAACS,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpDxC,OAAO4B,OAAO,CAACS,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,IAAI;QACF,MAAMvC,SAAS,MAAMqB,aAAa;YAChCK,SAAS;gBACP,GAAG5B,OAAO4B,OAAO;gBACjBE,MACEJ,UAAUgB,IAAAA,gCAAmB,EAACnC,QAAQU,OAAO,EAAEgB,aAAaP;YAChE;QACF;QACA,KAAK,MAAMiB,cAAc9C,kBAAmB;YAC1CK,OAAO0C,QAAQ,CAACC,OAAO,CAACN,MAAM,CAACI;QACjC;QACA,OAAOzC;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAO4B,OAAO,CAACE,IAAI,qBAAnB9B,sBAAqB8C,QAAQ;IACrC;AACF"}