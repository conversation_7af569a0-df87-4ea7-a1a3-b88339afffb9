/**
 * @license React
 * react-dom-server.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),fa=require("next/dist/compiled/react"),ha=require("react-dom");function ia(a){"function"===typeof a.flush&&a.flush()}var k=null,m=0,ja=!0;
function v(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(na(a,k.subarray(0,m)),k=new Uint8Array(2048),m=0),na(a,sa.encode(b));else{var c=k;0<m&&(c=k.subarray(m));c=sa.encodeInto(b,c);var d=c.read;m+=c.written;d<b.length&&(na(a,k.subarray(0,m)),k=new Uint8Array(2048),m=sa.encodeInto(b.slice(d),k).written);2048===m&&(na(a,k),k=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(na(a,k.subarray(0,m)),k=new Uint8Array(2048),m=0),na(a,b)):(c=k.length-m,c<
b.byteLength&&(0===c?na(a,k):(k.set(b.subarray(0,c),m),m+=c,na(a,k),b=b.subarray(c)),k=new Uint8Array(2048),m=0),k.set(b,m),m+=b.byteLength,2048===m&&(na(a,k),k=new Uint8Array(2048),m=0)))}function na(a,b){a=a.write(b);ja=ja&&a}function w(a,b){v(a,b);return ja}function ta(a){k&&0<m&&a.write(k.subarray(0,m));k=null;m=0;ja=!0}var sa=new aa.TextEncoder;function x(a){return sa.encode(a)}
var y=Object.assign,z=Object.prototype.hasOwnProperty,ua=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),va={},wa={};
function Ca(a){if(z.call(wa,a))return!0;if(z.call(va,a))return!1;if(ua.test(a))return wa[a]=!0;va[a]=!0;return!1}
var Da=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ea=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Fa=/["'&<>]/;
function A(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Fa.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ga=/([A-Z])/g,Ha=/^ms-/,Ia=Array.isArray,Ja=fa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ka=ha.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Wa={prefetchDNS:Pa,preconnect:Qa,preload:Ra,preloadModule:Sa,preinitStyle:Ta,preinitScript:Ua,preinitModuleScript:Va},Xa=[],ib=x('"></template>'),jb=x("<script>"),kb=x("\x3c/script>"),lb=x('<script src="'),mb=x('<script type="module" src="'),nb=x('" nonce="'),ob=x('" integrity="'),pb=x('" crossorigin="'),qb=x('" async="">\x3c/script>'),
rb=/(<\/|<)(s)(cript)/gi;function sb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var tb=x('<script type="importmap">'),ub=x("\x3c/script>");function B(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function vb(a,b,c){switch(b){case "noscript":return B(2,null,a.tagScope|1);case "select":return B(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return B(3,null,a.tagScope);case "picture":return B(2,null,a.tagScope|2);case "math":return B(4,null,a.tagScope);case "foreignObject":return B(2,null,a.tagScope);case "table":return B(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return B(6,null,a.tagScope);case "colgroup":return B(8,null,a.tagScope);case "tr":return B(7,null,a.tagScope)}return 5<=
a.insertionMode?B(2,null,a.tagScope):0===a.insertionMode?"html"===b?B(1,null,a.tagScope):B(2,null,a.tagScope):1===a.insertionMode?B(2,null,a.tagScope):a}var Eb=x("\x3c!-- --\x3e");function Fb(a,b,c,d){if(""===b)return d;d&&a.push(Eb);a.push(A(b));return!0}var Gb=new Map,Hb=x(' style="'),Ib=x(":"),Jb=x(";");
function Kb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(z.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=A(d);e=A((""+e).trim())}else f=Gb.get(d),void 0===f&&(f=x(A(d.replace(Ga,"-$1").toLowerCase().replace(Ha,"-ms-"))),Gb.set(d,f)),e="number"===typeof e?0===e||Da.has(d)?""+e:e+"px":
A((""+e).trim());c?(c=!1,a.push(Hb,f,Ib,e)):a.push(Jb,f,Ib,e)}}c||a.push(Lb)}var G=x(" "),Mb=x('="'),Lb=x('"'),Nb=x('=""');function Ob(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Nb)}function J(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(G,b,Mb,A(c),Lb)}x(A("javascript:throw new Error('A React form was unexpectedly submitted.')"));var Pb=x('<input type="hidden"');
function Qb(a,b){this.push(Pb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");J(this,"name",b);J(this,"value",a);this.push(Rb)}function Sb(a,b,c,d,e,f,g,h){null!=h&&N(a,"name",h);null!=d&&N(a,"formAction",d);null!=e&&N(a,"formEncType",e);null!=f&&N(a,"formMethod",f);null!=g&&N(a,"formTarget",g);return null}
function N(a,b,c){switch(b){case "className":J(a,"class",c);break;case "tabIndex":J(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":J(a,b,c);break;case "style":Kb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(G,b,Mb,A(""+c),Lb);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Ob(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(G,"xlink:href",Mb,A(""+c),Lb);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Mb,A(c),Lb);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Nb);break;case "capture":case "download":!0===c?a.push(G,b,Nb):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Mb,A(c),Lb);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(G,b,Mb,A(c),Lb);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(G,b,Mb,A(c),Lb);break;case "xlinkActuate":J(a,"xlink:actuate",c);break;case "xlinkArcrole":J(a,
"xlink:arcrole",c);break;case "xlinkRole":J(a,"xlink:role",c);break;case "xlinkShow":J(a,"xlink:show",c);break;case "xlinkTitle":J(a,"xlink:title",c);break;case "xlinkType":J(a,"xlink:type",c);break;case "xmlBase":J(a,"xml:base",c);break;case "xmlLang":J(a,"xml:lang",c);break;case "xmlSpace":J(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ea.get(b)||b,Ca(b)){switch(typeof c){case "function":case "symbol":return;case "boolean":var d=b.toLowerCase().slice(0,
5);if("data-"!==d&&"aria-"!==d)return}a.push(G,b,Mb,A(c),Lb)}}}var O=x(">"),Rb=x("/>");function Tb(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Ub(a){var b="";fa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var Vb=x(' selected=""');x('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
var Wb=x("\x3c!--F!--\x3e"),Xb=x("\x3c!--F--\x3e");
function Yb(a,b,c,d,e,f,g){var h=b.rel,l=b.href,n=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof l||""===l)return Q(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof n||null!=b.disabled||b.onLoad||b.onError)return Q(a,b);f=d.styles.get(n);g=c.styleResources.hasOwnProperty(l)?c.styleResources[l]:void 0;null!==g?(c.styleResources[l]=null,f||(f={precedence:A(n),rules:[],hrefs:[],sheets:new Map},d.styles.set(n,f)),b={state:0,props:y({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Zb(b.props,g),(c=d.preloads.stylesheets.get(l))&&0<c.length?c.length=0:b.state=1),f.sheets.set(l,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(l=f.sheets.get(l))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(l);e&&a.push(Eb);return null}if(b.onLoad||b.onError)return Q(a,b);e&&a.push(Eb);switch(b.rel){case "preconnect":case "dns-prefetch":return Q(d.preconnectChunks,b);case "preload":return Q(d.preloadChunks,b);default:return Q(d.hoistableChunks,
b)}}function Q(a,b){a.push(R("link"));for(var c in b)if(z.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:N(a,c,d)}}a.push(Rb);return null}
function $b(a,b,c){a.push(R(c));for(var d in b)if(z.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:N(a,d,e)}}a.push(Rb);return null}
function ac(a,b){a.push(R("title"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:N(a,e,f)}}a.push(O);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(A(""+b));Tb(a,d,c);a.push(bc,"title",cc);return null}
function mc(a,b){a.push(R("script"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:N(a,e,f)}}a.push(O);Tb(a,d,c);"string"===typeof c&&a.push(A(c));a.push(bc,"script",cc);return null}
function nc(a,b,c){a.push(R(c));var d=c=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:N(a,e,f)}}a.push(O);Tb(a,d,c);return"string"===typeof c?(a.push(A(c)),null):c}var oc=x("\n"),pc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,qc=new Map;function R(a){var b=qc.get(a);if(void 0===b){if(!pc.test(a))throw Error("Invalid tag: "+a);b=x("<"+a);qc.set(a,b)}return b}var rc=x("<!DOCTYPE html>");
function sc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(R("select"));var h=null,l=null,n;for(n in c)if(z.call(c,n)){var q=c[n];if(null!=q)switch(n){case "children":h=q;break;case "dangerouslySetInnerHTML":l=q;break;case "defaultValue":case "value":break;default:N(a,n,q)}}a.push(O);Tb(a,l,h);return h;case "option":var t=f.selectedValue;a.push(R("option"));var p=null,u=null,E=null,K=null,r;for(r in c)if(z.call(c,
r)){var C=c[r];if(null!=C)switch(r){case "children":p=C;break;case "selected":E=C;break;case "dangerouslySetInnerHTML":K=C;break;case "value":u=C;default:N(a,r,C)}}if(null!=t){var H=null!==u?""+u:Ub(p);if(Ia(t))for(var ka=0;ka<t.length;ka++){if(""+t[ka]===H){a.push(Vb);break}}else""+t===H&&a.push(Vb)}else E&&a.push(Vb);a.push(O);Tb(a,K,p);return p;case "textarea":a.push(R("textarea"));var D=null,T=null,F=null,ca;for(ca in c)if(z.call(c,ca)){var la=c[ca];if(null!=la)switch(ca){case "children":F=la;
break;case "value":D=la;break;case "defaultValue":T=la;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:N(a,ca,la)}}null===D&&null!==T&&(D=T);a.push(O);if(null!=F){if(null!=D)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ia(F)&&1<F.length)throw Error("<textarea> can only have at most one child.");D=""+F}"string"===typeof D&&"\n"===D[0]&&a.push(oc);null!==D&&a.push(A(""+D));return null;case "input":a.push(R("input"));
var U=null,da=null,L=null,X=null,M=null,oa=null,pa=null,qa=null,La=null,ea;for(ea in c)if(z.call(c,ea)){var Y=c[ea];if(null!=Y)switch(ea){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":U=Y;break;case "formAction":da=Y;break;case "formEncType":L=Y;break;case "formMethod":X=Y;break;case "formTarget":M=Y;break;case "defaultChecked":La=Y;break;case "defaultValue":pa=Y;break;case "checked":qa=
Y;break;case "value":oa=Y;break;default:N(a,ea,Y)}}var Vc=Sb(a,d,e,da,L,X,M,U);null!==qa?Ob(a,"checked",qa):null!==La&&Ob(a,"checked",La);null!==oa?N(a,"value",oa):null!==pa&&N(a,"value",pa);a.push(Rb);null!==Vc&&Vc.forEach(Qb,a);return null;case "button":a.push(R("button"));var Ya=null,Wc=null,Xc=null,Yc=null,Zc=null,$c=null,ad=null,Za;for(Za in c)if(z.call(c,Za)){var ma=c[Za];if(null!=ma)switch(Za){case "children":Ya=ma;break;case "dangerouslySetInnerHTML":Wc=ma;break;case "name":Xc=ma;break;case "formAction":Yc=
ma;break;case "formEncType":Zc=ma;break;case "formMethod":$c=ma;break;case "formTarget":ad=ma;break;default:N(a,Za,ma)}}var bd=Sb(a,d,e,Yc,Zc,$c,ad,Xc);a.push(O);null!==bd&&bd.forEach(Qb,a);Tb(a,Wc,Ya);if("string"===typeof Ya){a.push(A(Ya));var cd=null}else cd=Ya;return cd;case "form":a.push(R("form"));var $a=null,dd=null,dc=null,ec=null,fc=null,gc=null,ab;for(ab in c)if(z.call(c,ab)){var ra=c[ab];if(null!=ra)switch(ab){case "children":$a=ra;break;case "dangerouslySetInnerHTML":dd=ra;break;case "action":dc=
ra;break;case "encType":ec=ra;break;case "method":fc=ra;break;case "target":gc=ra;break;default:N(a,ab,ra)}}null!=dc&&N(a,"action",dc);null!=ec&&N(a,"encType",ec);null!=fc&&N(a,"method",fc);null!=gc&&N(a,"target",gc);a.push(O);Tb(a,dd,$a);if("string"===typeof $a){a.push(A($a));var ed=null}else ed=$a;return ed;case "menuitem":a.push(R("menuitem"));for(var wb in c)if(z.call(c,wb)){var fd=c[wb];if(null!=fd)switch(wb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");
default:N(a,wb,fd)}}a.push(O);return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var gd=ac(a,c);else ac(e.hoistableChunks,c),gd=null;return gd;case "link":return Yb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var hc=c.async;if("string"!==typeof c.src||!c.src||!hc||"function"===typeof hc||"symbol"===typeof hc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var hd=mc(a,c);else{var xb=c.src;if("module"===c.type){var yb=d.moduleScriptResources;
var id=e.preloads.moduleScripts}else yb=d.scriptResources,id=e.preloads.scripts;var zb=yb.hasOwnProperty(xb)?yb[xb]:void 0;if(null!==zb){yb[xb]=null;var ic=c;if(zb){2===zb.length&&(ic=y({},c),Zb(ic,zb));var jd=id.get(xb);jd&&(jd.length=0)}var kd=[];e.scripts.add(kd);mc(kd,ic)}g&&a.push(Eb);hd=null}return hd;case "style":var Ab=c.precedence,xa=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Ab||"string"!==typeof xa||""===xa){a.push(R("style"));var Ma=null,ld=null,bb;
for(bb in c)if(z.call(c,bb)){var Bb=c[bb];if(null!=Bb)switch(bb){case "children":Ma=Bb;break;case "dangerouslySetInnerHTML":ld=Bb;break;default:N(a,bb,Bb)}}a.push(O);var cb=Array.isArray(Ma)?2>Ma.length?Ma[0]:null:Ma;"function"!==typeof cb&&"symbol"!==typeof cb&&null!==cb&&void 0!==cb&&a.push(A(""+cb));Tb(a,ld,Ma);a.push(bc,"style",cc);var md=null}else{var ya=e.styles.get(Ab);if(null!==(d.styleResources.hasOwnProperty(xa)?d.styleResources[xa]:void 0)){d.styleResources[xa]=null;ya?ya.hrefs.push(A(xa)):
(ya={precedence:A(Ab),rules:[],hrefs:[A(xa)],sheets:new Map},e.styles.set(Ab,ya));var nd=ya.rules,Na=null,od=null,Cb;for(Cb in c)if(z.call(c,Cb)){var jc=c[Cb];if(null!=jc)switch(Cb){case "children":Na=jc;break;case "dangerouslySetInnerHTML":od=jc}}var db=Array.isArray(Na)?2>Na.length?Na[0]:null:Na;"function"!==typeof db&&"symbol"!==typeof db&&null!==db&&void 0!==db&&nd.push(A(""+db));Tb(nd,od,Na)}ya&&e.boundaryResources&&e.boundaryResources.styles.add(ya);g&&a.push(Eb);md=void 0}return md;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var pd=$b(a,c,"meta");else g&&a.push(Eb),pd="string"===typeof c.charSet?$b(e.charsetChunks,c,"meta"):"viewport"===c.name?$b(e.preconnectChunks,c,"meta"):$b(e.hoistableChunks,c,"meta");return pd;case "listing":case "pre":a.push(R(b));var eb=null,fb=null,gb;for(gb in c)if(z.call(c,gb)){var Db=c[gb];if(null!=Db)switch(gb){case "children":eb=Db;break;case "dangerouslySetInnerHTML":fb=Db;break;default:N(a,gb,Db)}}a.push(O);if(null!=fb){if(null!=eb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof fb||!("__html"in fb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var za=fb.__html;null!==za&&void 0!==za&&("string"===typeof za&&0<za.length&&"\n"===za[0]?a.push(oc,za):a.push(""+za))}"string"===typeof eb&&"\n"===eb[0]&&a.push(oc);return eb;case "img":var P=c.src,I=c.srcSet;if(!("lazy"===c.loading||!P&&!I||"string"!==typeof P&&null!=P||"string"!==typeof I&&
null!=I)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof P||":"!==P[4]||"d"!==P[0]&&"D"!==P[0]||"a"!==P[1]&&"A"!==P[1]||"t"!==P[2]&&"T"!==P[2]||"a"!==P[3]&&"A"!==P[3])&&("string"!==typeof I||":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var qd="string"===typeof c.sizes?c.sizes:void 0,hb=I?I+"\n"+(qd||""):P,kc=e.preloads.images,Aa=kc.get(hb);if(Aa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)kc.delete(hb),
e.highImagePreloads.add(Aa)}else d.imageResources.hasOwnProperty(hb)||(d.imageResources[hb]=Xa,Aa=[],Q(Aa,{rel:"preload",as:"image",href:I?void 0:P,imageSrcSet:I,imageSizes:qd,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Aa):(e.bulkPreloads.add(Aa),kc.set(hb,Aa)))}return $b(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return $b(a,
c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var rd=nc(e.headChunks,c,"head")}else rd=nc(a,c,"head");return rd;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[rc];var sd=nc(e.htmlChunks,c,"html")}else sd=nc(a,c,"html");return sd;default:if(-1!==b.indexOf("-")){a.push(R(b));
var lc=null,td=null,Oa;for(Oa in c)if(z.call(c,Oa)){var Ba=c[Oa];if(null!=Ba)switch(Oa){case "children":lc=Ba;break;case "dangerouslySetInnerHTML":td=Ba;break;case "style":Kb(a,Ba);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Ca(Oa)&&"function"!==typeof Ba&&"symbol"!==typeof Ba&&a.push(G,Oa,Mb,A(Ba),Lb)}}a.push(O);Tb(a,td,lc);return lc}}return nc(a,c,b)}var bc=x("</"),cc=x(">");
function tc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)v(a,b[c]);return c<b.length?(c=b[c],b.length=0,w(a,c)):!0}var uc=x('<template id="'),vc=x('"></template>'),wc=x("\x3c!--$--\x3e"),xc=x('\x3c!--$?--\x3e<template id="'),yc=x('"></template>'),zc=x("\x3c!--$!--\x3e"),Ac=x("\x3c!--/$--\x3e"),Bc=x("<template"),Cc=x('"'),Dc=x(' data-dgst="');x(' data-msg="');x(' data-stck="');var Ec=x("></template>");
function Fc(a,b,c){v(a,xc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");v(a,b.boundaryPrefix);v(a,c.toString(16));return w(a,yc)}
var Gc=x('<div hidden id="'),Hc=x('">'),Ic=x("</div>"),Jc=x('<svg aria-hidden="true" style="display:none" id="'),Kc=x('">'),Lc=x("</svg>"),Mc=x('<math aria-hidden="true" style="display:none" id="'),Nc=x('">'),Oc=x("</math>"),Pc=x('<table hidden id="'),Qc=x('">'),Rc=x("</table>"),Sc=x('<table hidden><tbody id="'),Tc=x('">'),Uc=x("</tbody></table>"),ud=x('<table hidden><tr id="'),vd=x('">'),wd=x("</tr></table>"),xd=x('<table hidden><colgroup id="'),yd=x('">'),zd=x("</colgroup></table>");
function Ad(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return v(a,Gc),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,Hc);case 3:return v(a,Jc),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,Kc);case 4:return v(a,Mc),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,Nc);case 5:return v(a,Pc),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,Qc);case 6:return v(a,Sc),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,Tc);case 7:return v(a,ud),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,vd);case 8:return v(a,
xd),v(a,b.segmentPrefix),v(a,d.toString(16)),w(a,yd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function Bd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return w(a,Ic);case 3:return w(a,Lc);case 4:return w(a,Oc);case 5:return w(a,Rc);case 6:return w(a,Uc);case 7:return w(a,wd);case 8:return w(a,zd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var Cd=x('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),Dd=x('$RS("'),Ed=x('","'),Fd=x('")\x3c/script>'),Gd=x('<template data-rsi="" data-sid="'),Hd=x('" data-pid="'),Id=x('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Jd=x('$RC("'),Kd=x('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Ld=x('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Md=x('$RR("'),Nd=x('","'),Od=x('",'),Pd=x('"'),Qd=x(")\x3c/script>"),Rd=x('<template data-rci="" data-bid="'),Sd=x('<template data-rri="" data-bid="'),Td=x('" data-sid="'),Ud=x('" data-sty="'),Vd=x('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),Wd=x('$RX("'),Xd=x('"'),Yd=x(","),Zd=x(")\x3c/script>"),$d=x('<template data-rxi="" data-bid="'),ae=x('" data-dgst="'),
be=x('" data-msg="'),ce=x('" data-stck="'),de=/[<\u2028\u2029]/g;function ee(a){return JSON.stringify(a).replace(de,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var fe=/[&><\u2028\u2029]/g;
function ge(a){return JSON.stringify(a).replace(fe,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var he=x('<style media="not all" data-precedence="'),ie=x('" data-href="'),je=x('">'),ke=x("</style>"),le=!1,me=!0;function ne(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){v(this,he);v(this,a.precedence);for(v(this,ie);d<c.length-1;d++)v(this,c[d]),v(this,oe);v(this,c[d]);v(this,je);for(d=0;d<b.length;d++)v(this,b[d]);me=w(this,ke);le=!0;b.length=0;c.length=0}}function pe(a){return 2!==a.state?le=!0:!1}
function qe(a,b,c){le=!1;me=!0;b.styles.forEach(ne,a);b.stylesheets.forEach(pe);le&&(c.stylesToHoist=!0);return me}function S(a){for(var b=0;b<a.length;b++)v(this,a[b]);a.length=0}var re=[];function se(a){Q(re,a.props);for(var b=0;b<re.length;b++)v(this,re[b]);re.length=0;a.state=2}var te=x('<style data-precedence="'),ue=x('" data-href="'),oe=x(" "),ve=x('">'),we=x("</style>");
function xe(a){var b=0<a.sheets.size;a.sheets.forEach(se,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){v(this,te);v(this,a.precedence);a=0;if(d.length){for(v(this,ue);a<d.length-1;a++)v(this,d[a]),v(this,oe);v(this,d[a])}v(this,ve);for(a=0;a<c.length;a++)v(this,c[a]);v(this,we);c.length=0;d.length=0}}
function ye(a){if(0===a.state){a.state=1;var b=a.props;Q(re,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<re.length;a++)v(this,re[a]);re.length=0}}function ze(a){a.sheets.forEach(ye,this);a.sheets.clear()}var Ae=x("["),Be=x(",["),Ce=x(","),De=x("]");
function Ee(a,b){v(a,Ae);var c=Ae;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,ge(""+d.props.href)),v(a,De),c=Be;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,ge(""+d.props.href));e=""+e;v(a,Ce);v(a,ge(e));for(var g in f)if(z.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ca(g))break a;h=""+h}v(e,Ce);v(e,ge(l));v(e,Ce);v(e,ge(h))}}}v(a,
De);c=Be;d.state=3}});v(a,De)}
function Fe(a,b){v(a,Ae);var c=Ae;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,A(JSON.stringify(""+d.props.href))),v(a,De),c=Be;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,A(JSON.stringify(""+d.props.href)));e=""+e;v(a,Ce);v(a,A(JSON.stringify(e)));for(var g in f)if(z.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ca(g))break a;h=""+h}v(e,Ce);v(e,A(JSON.stringify(l)));v(e,Ce);v(e,A(JSON.stringify(h)))}}}v(a,
De);c=Be;d.state=3}});v(a,De)}function Pa(a){var b=Ge();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;Q(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}He(b)}}}
function Qa(a,b){var c=Ge();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;Q(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}He(c)}}}
function Ra(a,b,c){var d=Ge();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=Xa;e=[];Q(e,y({rel:"preload",href:g?void 0:a,as:b},c));"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];Q(g,y({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?Xa:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);Q(g,y({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?Xa:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=y({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}Q(e,c);g[a]=Xa}He(d)}}}
function Sa(a,b){var c=Ge();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?Xa:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=Xa}Q(f,y({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);He(c)}}}
function Ta(a,b,c){var d=Ge();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:A(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:y({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Zb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),He(d))}}}
function Ua(a,b){var c=Ge();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=y({src:a,async:!0},b),f&&(2===f.length&&Zb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),mc(a,b),He(c))}}}
function Va(a,b){var c=Ge();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=y({src:a,type:"module",async:!0},b),f&&(2===f.length&&Zb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),mc(a,b),He(c))}}}function Zb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function Ie(a){this.styles.add(a)}
function Je(a){this.stylesheets.add(a)}
var Ke=new ba.AsyncLocalStorage,Le=Symbol.for("react.element"),Me=Symbol.for("react.portal"),Ne=Symbol.for("react.fragment"),Oe=Symbol.for("react.strict_mode"),Pe=Symbol.for("react.profiler"),Qe=Symbol.for("react.provider"),Re=Symbol.for("react.context"),Se=Symbol.for("react.server_context"),Te=Symbol.for("react.forward_ref"),Ue=Symbol.for("react.suspense"),Ve=Symbol.for("react.suspense_list"),We=Symbol.for("react.memo"),Xe=Symbol.for("react.lazy"),Ye=Symbol.for("react.scope"),Ze=Symbol.for("react.debug_trace_mode"),
$e=Symbol.for("react.offscreen"),af=Symbol.for("react.legacy_hidden"),bf=Symbol.for("react.cache"),cf=Symbol.for("react.default_value"),df=Symbol.iterator;
function ef(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Ne:return"Fragment";case Me:return"Portal";case Pe:return"Profiler";case Oe:return"StrictMode";case Ue:return"Suspense";case Ve:return"SuspenseList";case bf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Re:return(a.displayName||"Context")+".Consumer";case Qe:return(a._context.displayName||"Context")+".Provider";case Te:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case We:return b=a.displayName||null,null!==b?b:ef(a.type)||"Memo";case Xe:b=a._payload;a=a._init;try{return ef(a(b))}catch(c){break}case Se:return(a.displayName||a._globalName)+".Provider"}return null}var ff={};function gf(a,b){a=a.contextTypes;if(!a)return ff;var c={},d;for(d in a)c[d]=b[d];return c}var hf=null;
function jf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");jf(a,c)}b.context._currentValue=b.value}}function kf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&kf(a)}
function lf(a){var b=a.parent;null!==b&&lf(b);a.context._currentValue=a.value}function mf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?jf(a,b):mf(a,b)}
function nf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?jf(a,c):nf(a,c);b.context._currentValue=b.value}function of(a){var b=hf;b!==a&&(null===b?lf(a):null===a?kf(b):b.depth===a.depth?jf(b,a):b.depth>a.depth?mf(b,a):nf(b,a),hf=a)}
var pf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function qf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=pf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:y({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&pf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=y({},f,h)):y(f,h))}a.state=f}else f.queue=null}
var rf={id:1,overflow:""};function sf(a,b,c){var d=a.id;a=a.overflow;var e=32-tf(d)-1;d&=~(1<<e);c+=1;var f=32-tf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-tf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var tf=Math.clz32?Math.clz32:uf,vf=Math.log,wf=Math.LN2;function uf(a){a>>>=0;return 0===a?32:31-(vf(a)/wf|0)|0}var xf=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function yf(){}function zf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(yf,yf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Af=b;throw xf;}}var Af=null;
function Bf(){if(null===Af)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Af;Af=null;return a}function Cf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Df="function"===typeof Object.is?Object.is:Cf,Ef=null,Ff=null,Gf=null,V=null,Hf=!1,If=!1,Jf=0,Kf=0,Lf=-1,Mf=0,Nf=null,Of=null,Pf=0;
function Qf(){if(null===Ef)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return Ef}
function Rf(){if(0<Pf)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function Sf(){null===V?null===Gf?(Hf=!1,Gf=V=Rf()):(Hf=!0,V=Gf):null===V.next?(Hf=!1,V=V.next=Rf()):(Hf=!0,V=V.next);return V}function Tf(a,b,c,d){for(;If;)If=!1,Kf=Jf=0,Lf=-1,Mf=0,Pf+=1,V=null,c=a(b,d);Uf();return c}function Vf(){var a=Nf;Nf=null;return a}function Uf(){Ff=Ef=null;If=!1;Gf=null;Pf=0;V=Of=null}function Wf(a,b){return"function"===typeof b?b(a):b}
function Xf(a,b,c){Ef=Qf();V=Sf();if(Hf){var d=V.queue;b=d.dispatch;if(null!==Of&&(c=Of.get(d),void 0!==c)){Of.delete(d);d=V.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);V.memoizedState=d;return[d,b]}return[V.memoizedState,b]}a=a===Wf?"function"===typeof b?b():b:void 0!==c?c(b):b;V.memoizedState=a;a=V.queue={last:null,dispatch:null};a=a.dispatch=Yf.bind(null,Ef,a);return[V.memoizedState,a]}
function Zf(a,b){Ef=Qf();V=Sf();b=void 0===b?null:b;if(null!==V){var c=V.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Df(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();V.memoizedState=[a,b];return a}
function Yf(a,b,c){if(25<=Pf)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===Ef)if(If=!0,a={action:c,next:null},null===Of&&(Of=new Map),c=Of.get(b),void 0===c)Of.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function $f(){throw Error("startTransition cannot be called during server rendering.");}function ag(a){var b=Mf;Mf+=1;null===Nf&&(Nf=[]);return zf(Nf,a,b)}
function bg(){throw Error("Cache cannot be refreshed during server rendering.");}function cg(){}
var eg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ag(a);if(a.$$typeof===Re||a.$$typeof===Se)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){Qf();return a._currentValue},useMemo:Zf,useReducer:Xf,useRef:function(a){Ef=Qf();V=Sf();var b=V.memoizedState;return null===b?(a={current:a},V.memoizedState=a):b},useState:function(a){return Xf(Wf,a)},
useInsertionEffect:cg,useLayoutEffect:cg,useCallback:function(a,b){return Zf(function(){return a},b)},useImperativeHandle:cg,useEffect:cg,useDebugValue:cg,useDeferredValue:function(a){Qf();return a},useTransition:function(){Qf();return[!1,$f]},useId:function(){var a=Ff.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-tf(a)-1)).toString(32)+b;var c=dg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Jf++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return bg}},dg=null,fg={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},gg=Ja.ReactCurrentDispatcher,hg=Ja.ReactCurrentCache;function ig(a){console.error(a);return null}
function jg(){}var kg=null;function Ge(){if(kg)return kg;var a=Ke.getStore();return a?a:null}function lg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return mg(a)}))}function ng(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function og(a,b,c,d,e,f,g,h,l,n,q,t){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var p={replay:null,node:c,childIndex:d,ping:function(){return lg(a,p)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:t,thenableState:b};g.add(p);return p}
function pg(a,b,c,d,e,f,g,h,l,n,q,t){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var p={replay:c,node:d,childIndex:e,ping:function(){return lg(a,p)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:t,thenableState:b};g.add(p);return p}function qg(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function W(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function rg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function sg(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((ef(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=y({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function tg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var n=0;n<f;n++)n===g?l.push(Wb):l.push(Xb)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=sf(c,1,0),ug(a,b,d,-1),b.treeContext=c):h?ug(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function vg(a,b){if(a&&a.defaultProps){b=y({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function wg(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=gf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:d);qf(h,e,f,d);sg(a,b,c,h,e)}else{h=gf(e,b.legacyContext);Ef={};Ff=b;Kf=Jf=0;Lf=-1;Mf=0;Nf=d;d=e(f,h);d=Tf(e,f,d,h);g=0!==Jf;var l=Kf,n=Lf;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(qf(d,e,f,h),sg(a,b,c,d,e)):tg(a,b,c,d,g,l,n)}else if("string"===typeof e)if(d=b.blockedSegment,
null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=vb(h,e,f),b.keyPath=c,ug(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=sc(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;l=b.keyPath;b.formatContext=vb(h,e,f);b.keyPath=c;ug(a,b,g,-1);b.formatContext=h;b.keyPath=l;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(bc,e,cc)}d.lastPushedText=!1}else{switch(e){case af:case Ze:case Oe:case Pe:case Ne:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case $e:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case Ve:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case Ye:throw Error("ReactDOMServer does not yet support scope components.");
case Ue:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{ug(a,b,c,-1)}finally{b.keyPath=e}}else{n=b.keyPath;e=b.blockedBoundary;var q=b.blockedSegment;d=f.fallback;var t=f.children;f=new Set;g=ng(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);l=qg(a,q.chunks.length,g,b.formatContext,!1,!1);q.children.push(l);q.lastPushedText=!1;var p=qg(a,0,null,b.formatContext,!1,!1);p.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=p;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(ug(a,b,t,-1),p.lastPushedText&&p.textEmbedded&&p.chunks.push(Eb),p.status=1,xg(g,p),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(u){p.status=4,g.status=4,h=W(a,u),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=q,b.keyPath=n}h=[c[0],"Suspense Fallback",c[2]];n=a.trackedPostpones;null!==n&&(q=[h[1],h[2],[],null],n.workingMap.set(h,q),5===g.status?n.workingMap.get(c)[4]=q:g.trackedFallbackNode=q);b=og(a,null,d,-1,
e,l,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Te:e=e.render;Ef={};Ff=b;Kf=Jf=0;Lf=-1;Mf=0;Nf=d;d=e(f,g);f=Tf(e,f,d,g);tg(a,b,c,f,0!==Jf,Kf,Lf);return;case We:e=e.type;f=vg(e,f);wg(a,b,c,d,e,f,g);return;case Qe:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue;e._currentValue=f;l=hf;hf=f={parent:l,depth:null===l?0:l.depth+1,context:e,parentValue:g,value:f};b.context=f;b.keyPath=
c;Z(a,b,null,h,-1);a=hf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===cf?a.context._defaultValue:c;a=hf=a.parent;b.context=a;b.keyPath=d;return;case Re:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case Xe:h=e._init;e=h(e._payload);f=vg(e,f);wg(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function yg(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=qg(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,ug(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(xg(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Le:var f=d.type,g=d.key,h=d.props,l=d.ref,n=ef(f),q=null==g?-1===e?0:e:g;g=[b.keyPath,n,q];if(null!==b.replay)a:{var t=b.replay;e=t.nodes;for(d=0;d<e.length;d++){var p=e[d];if(q===p[1]){if(null!==n&&n!==p[0])throw Error('Expected to see a component of type "'+n+"\" in this slot. The tree doesn't match so React will fallback to client rendering.");if(4===p.length){n=p[2];p=p[3];b.replay={nodes:n,
slots:p,pendingTasks:1};try{if("number"===typeof p){q=a;var u=b,E=u.replay,K=u.blockedBoundary,r=qg(q,0,null,u.formatContext,!1,!1);r.id=p;r.parentFlushed=!0;try{u.replay=null,u.blockedSegment=r,wg(q,u,g,c,f,h,l),r.status=1,null===K?q.completedRootSegment=r:(xg(K,r),K.parentFlushed&&q.partialBoundaries.push(K))}finally{u.replay=E,u.blockedSegment=null}}else wg(a,b,g,c,f,h,l);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
}catch(H){if("object"===typeof H&&null!==H&&(H===xf||"function"===typeof H.then))throw H;h=void 0;var C=b.blockedBoundary;g=H;h=W(a,g);zg(a,C,n,p,g,h)}finally{b.replay.pendingTasks--,b.replay=t}}else{if(f!==Ue)throw Error("Expected to see a Suspense boundary in this slot. The tree doesn't match so React will fallback to client rendering.");b:{C=void 0;E=p[5];K=p[2];r=p[3];f=null===p[4]?[]:p[4][2];t=null===p[4]?null:p[4][3];l=b.keyPath;n=b.replay;p=b.blockedBoundary;q=h.children;h=h.fallback;c=new Set;
u=ng(a,c);u.parentFlushed=!0;u.rootSegmentID=E;b.blockedBoundary=u;b.replay={nodes:K,slots:r,pendingTasks:1};a.renderState.boundaryResources=u.resources;try{"number"===typeof r?yg(a,b,r,q,-1):ug(a,b,q,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===u.pendingTasks&&0===u.status){u.status=1;a.completedBoundaries.push(u);
break b}}catch(H){u.status=4,C=W(a,H),u.errorDigest=C,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(u)}finally{a.renderState.boundaryResources=p?p.resources:null,b.blockedBoundary=p,b.replay=n,b.keyPath=l}g=[g[0],"Suspense Fallback",g[2]];"number"===typeof t?(C=qg(a,0,null,b.formatContext,!1,!1),C.id=t,C.parentFlushed=!0,b=og(a,null,h,-1,p,C,c,g,b.formatContext,b.legacyContext,b.context,b.treeContext)):b=pg(a,null,{nodes:f,slots:t,pendingTasks:0},h,-1,p,c,g,b.formatContext,b.legacyContext,
b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else wg(a,b,g,c,f,h,l);return;case Me:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case Xe:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(Ia(d)){Ag(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=df&&d[df]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),
d=h.next();while(!d.done);Ag(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,ag(d),e);if(d.$$typeof===Re||d.$$typeof===Se)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Fb(e.chunks,
d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Fb(e.chunks,""+d,a.renderState,e.lastPushedText)))}
function Ag(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{if(Ag(a,b,c,-1),1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");}catch(t){if("object"===typeof t&&null!==t&&(t===xf||
"function"===typeof t.then))throw t;c=void 0;var n=b.blockedBoundary,q=t;c=W(a,q);zg(a,n,d,l,q,c)}finally{b.replay.pendingTasks--,b.replay=f}g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(n=0;n<g;n++)d=c[n],b.treeContext=sf(f,g,n),l=h[n],"number"===typeof l?(yg(a,b,l,d,n),delete h[n]):ug(a,b,d,n);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)n=c[h],b.treeContext=sf(f,g,h),ug(a,b,n,h);b.treeContext=f;
b.keyPath=e}
function ug(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,n=b.blockedSegment;if(null===n)try{return Z(a,b,null,c,d)}catch(p){if(Uf(),c=p===xf?Bf():p,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Vf();a=pg(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;of(g);return}}else{var q=
n.children.length,t=n.chunks.length;try{return Z(a,b,null,c,d)}catch(p){if(Uf(),n.children.length=q,n.chunks.length=t,c=p===xf?Bf():p,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Vf();n=b.blockedSegment;q=qg(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(q);n.lastPushedText=!1;a=og(a,d,b.node,b.childIndex,b.blockedBoundary,q,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=l;of(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;of(g);throw c;}function Bg(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,Cg(this,b,a))}
function zg(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)zg(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,n=f,q=ng(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=n;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var t in d)delete d[t]}}
function Dg(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);null===d?(b.allPendingTasks--,1!==b.status&&2!==b.status&&(a=a.replay,null===a?(W(b,c),rg(b,c)):(a.pendingTasks--,0===a.pendingTasks&&0<a.nodes.length&&(d=W(b,c),zg(b,null,a.nodes,a.slots,c,d))))):(d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=W(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return Dg(f,b,c)}),d.fallbackAbortableTasks.clear(),b.allPendingTasks--,
0===b.allPendingTasks&&(a=b.onAllReady,a()))}function xg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&xg(a,c)}else a.completedSegments.push(b)}
function Cg(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=jg,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&xg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Bg,
a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&(xg(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function mg(a){if(2!==a.status){var b=hf,c=gg.current;gg.current=eg;var d=hg.current;hg.current=fg;var e=kg;kg=a;var f=dg;dg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],n=a,q=l.blockedBoundary;n.renderState.boundaryResources=q?q.resources:null;var t=l.blockedSegment;if(null===t){var p=n;if(0!==l.replay.pendingTasks){of(l.context);try{var u=l.thenableState;l.thenableState=null;Z(p,l,u,l.node,-1);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);Cg(p,l.blockedBoundary,null)}catch(M){Uf();var E=M===xf?Bf():M;if("object"===typeof E&&null!==E&&"function"===typeof E.then){var K=l.ping;E.then(K,K);l.thenableState=Vf()}else{l.replay.pendingTasks--;l.abortSet.delete(l);n=void 0;var r=p,C=l.blockedBoundary,H=E,ka=l.replay.nodes,D=l.replay.slots;n=W(r,H);zg(r,C,ka,D,H,n);p.allPendingTasks--;if(0===p.allPendingTasks){var T=p.onAllReady;T()}}}finally{p.renderState.boundaryResources=null}}}else if(p=void 0,
r=t,0===r.status){of(l.context);var F=r.children.length,ca=r.chunks.length;try{var la=l.thenableState;l.thenableState=null;Z(n,l,la,l.node,l.childIndex);r.lastPushedText&&r.textEmbedded&&r.chunks.push(Eb);l.abortSet.delete(l);r.status=1;Cg(n,l.blockedBoundary,r)}catch(M){Uf();r.children.length=F;r.chunks.length=ca;var U=M===xf?Bf():M;if("object"===typeof U&&null!==U&&"function"===typeof U.then){var da=l.ping;U.then(da,da);l.thenableState=Vf()}else{l.abortSet.delete(l);r.status=4;var L=l.blockedBoundary;
p=W(n,U);null===L?rg(n,U):(L.pendingTasks--,4!==L.status&&(L.status=4,L.errorDigest=p,L.parentFlushed&&n.clientRenderedBoundaries.push(L)));n.allPendingTasks--;if(0===n.allPendingTasks){var X=n.onAllReady;X()}}}finally{n.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&Eg(a,a.destination)}catch(M){W(a,M),rg(a,M)}finally{dg=f,gg.current=c,hg.current=d,c===eg&&of(b),kg=e}}}
function Fg(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;v(b,uc);v(b,a.placeholderPrefix);a=d.toString(16);v(b,a);return w(b,vc);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)v(b,d[f]);e=Gg(a,b,e)}for(;f<d.length-1;f++)v(b,d[f]);f<d.length&&(e=w(b,d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function Gg(a,b,c){var d=c.boundary;if(null===d)return Fg(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,w(b,zc),v(b,Bc),d&&(v(b,Dc),v(b,A(d)),v(b,Cc)),w(b,Ec),Fg(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Fc(b,a.renderState,d.rootSegmentID),Fg(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Fc(b,a.renderState,d.rootSegmentID),Fg(a,b,
c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(Ie,e),c.stylesheets.forEach(Je,e));w(b,wc);d=d.completedSegments;if(1!==d.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");Gg(a,b,d[0])}return w(b,Ac)}function Hg(a,b,c){Ad(b,a.renderState,c.parentFormatContext,c.id);Gg(a,b,c);return Bd(b,c.parentFormatContext)}
function Ig(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Jg(a,b,c,d[e]);d.length=0;qe(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(v(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,v(b,2048<Kd.length?Kd.slice():Kd)):0===(d.instructions&8)?(d.instructions|=8,v(b,Ld)):v(b,Md):0===(d.instructions&2)?(d.instructions|=
2,v(b,Id)):v(b,Jd)):f?v(b,Sd):v(b,Rd);d=e.toString(16);v(b,a.boundaryPrefix);v(b,d);g?v(b,Nd):v(b,Td);v(b,a.segmentPrefix);v(b,d);f?g?(v(b,Od),Ee(b,c)):(v(b,Ud),Fe(b,c)):g&&v(b,Pd);d=g?w(b,Qd):w(b,ib);return tc(b,a)&&d}
function Jg(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Hg(a,b,d)}if(e===c.rootSegmentID)return Hg(a,b,d);Hg(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(v(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,v(b,Cd)):v(b,Dd)):v(b,Gd);v(b,a.segmentPrefix);e=e.toString(16);v(b,e);d?v(b,Ed):v(b,Hd);v(b,a.placeholderPrefix);v(b,
e);b=d?w(b,Fd):w(b,ib);return b}
function Eg(a,b){k=new Uint8Array(2048);m=0;ja=!0;try{var c,d=a.completedRootSegment;if(null!==d)if(0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var n=e.htmlChunks,q=e.headChunks;f=0;if(n){for(f=0;f<n.length;f++)v(b,n[f]);if(q)for(f=0;f<q.length;f++)v(b,q[f]);else v(b,R("head")),
v(b,O)}else if(q)for(f=0;f<q.length;f++)v(b,q[f]);var t=e.charsetChunks;for(f=0;f<t.length;f++)v(b,t[f]);t.length=0;e.preconnects.forEach(S,b);e.preconnects.clear();var p=e.preconnectChunks;for(f=0;f<p.length;f++)v(b,p[f]);p.length=0;e.fontPreloads.forEach(S,b);e.fontPreloads.clear();e.highImagePreloads.forEach(S,b);e.highImagePreloads.clear();e.styles.forEach(xe,b);var u=e.importMapChunks;for(f=0;f<u.length;f++)v(b,u[f]);u.length=0;e.bootstrapScripts.forEach(S,b);e.scripts.forEach(S,b);e.scripts.clear();
e.bulkPreloads.forEach(S,b);e.bulkPreloads.clear();var E=e.preloadChunks;for(f=0;f<E.length;f++)v(b,E[f]);E.length=0;var K=e.hoistableChunks;for(f=0;f<K.length;f++)v(b,K[f]);K.length=0;n&&null===q&&(v(b,bc),v(b,"head"),v(b,cc));Gg(a,b,d);a.completedRootSegment=null;tc(b,a.renderState)}else return;var r=a.renderState;d=0;r.preconnects.forEach(S,b);r.preconnects.clear();var C=r.preconnectChunks;for(d=0;d<C.length;d++)v(b,C[d]);C.length=0;r.fontPreloads.forEach(S,b);r.fontPreloads.clear();r.highImagePreloads.forEach(S,
b);r.highImagePreloads.clear();r.styles.forEach(ze,b);r.scripts.forEach(S,b);r.scripts.clear();r.bulkPreloads.forEach(S,b);r.bulkPreloads.clear();var H=r.preloadChunks;for(d=0;d<H.length;d++)v(b,H[d]);H.length=0;var ka=r.hoistableChunks;for(d=0;d<ka.length;d++)v(b,ka[d]);ka.length=0;var D=a.clientRenderedBoundaries;for(c=0;c<D.length;c++){var T=D[c];r=b;var F=a.resumableState,ca=a.renderState,la=T.rootSegmentID,U=T.errorDigest,da=T.errorMessage,L=T.errorComponentStack,X=0===F.streamingFormat;X?(v(r,
ca.startInlineScript),0===(F.instructions&4)?(F.instructions|=4,v(r,Vd)):v(r,Wd)):v(r,$d);v(r,ca.boundaryPrefix);v(r,la.toString(16));X&&v(r,Xd);if(U||da||L)X?(v(r,Yd),v(r,ee(U||""))):(v(r,ae),v(r,A(U||"")));if(da||L)X?(v(r,Yd),v(r,ee(da||""))):(v(r,be),v(r,A(da||"")));L&&(X?(v(r,Yd),v(r,ee(L))):(v(r,ce),v(r,A(L))));if(X?!w(r,Zd):!w(r,ib)){a.destination=null;c++;D.splice(0,c);return}}D.splice(0,c);var M=a.completedBoundaries;for(c=0;c<M.length;c++)if(!Ig(a,b,M[c])){a.destination=null;c++;M.splice(0,
c);return}M.splice(0,c);ta(b);k=new Uint8Array(2048);m=0;ja=!0;var oa=a.partialBoundaries;for(c=0;c<oa.length;c++){var pa=oa[c];a:{D=a;T=b;D.renderState.boundaryResources=pa.resources;var qa=pa.completedSegments;for(F=0;F<qa.length;F++)if(!Jg(D,T,pa,qa[F])){F++;qa.splice(0,F);var La=!1;break a}qa.splice(0,F);La=qe(T,pa.resources,D.renderState)}if(!La){a.destination=null;c++;oa.splice(0,c);return}}oa.splice(0,c);var ea=a.completedBoundaries;for(c=0;c<ea.length;c++)if(!Ig(a,b,ea[c])){a.destination=
null;c++;ea.splice(0,c);return}ea.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(v(b,bc),v(b,"body"),v(b,cc)),c.hasHtml&&(v(b,bc),v(b,"html"),v(b,cc)),ta(b),ia(b),b.end(),a.destination=null):(ta(b),ia(b))}}function Kg(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return Ke.run(a,mg,a)})}
function He(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setImmediate(function(){var b=a.destination;b?Eg(a,b):a.flushScheduled=!1}))}function Lg(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Eg(a,b)}catch(c){W(a,c),rg(a,c)}}}
function Mg(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return Dg(e,a,d)});c.clear()}null!==a.destination&&Eg(a,a.destination)}catch(e){W(a,e),rg(a,e)}}function Ng(a,b){return function(){return Lg(b,a)}}function Og(a,b){return function(){a.destination=null;Mg(a,Error(b))}}
function Pg(a,b){var c=b?b.identifierPrefix:void 0;var d=0;void 0!==(b?b.unstable_externalRuntimeSrc:void 0)&&(d=1);c={idPrefix:void 0===c?"":c,nextFormID:0,streamingFormat:d,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}};var e=b?b.nonce:void 0,f=b?b.bootstrapScriptContent:void 0,g=b?b.bootstrapScripts:void 0,
h=b?b.bootstrapModules:void 0,l=b?b.unstable_externalRuntimeSrc:void 0;d=b?b.importMap:void 0;var n=void 0===e?jb:x('<script nonce="'+A(e)+'">'),q=c.idPrefix,t=[],p=null;void 0!==f&&t.push(n,(""+f).replace(rb,sb),kb);void 0!==l&&("string"===typeof l?(p={src:l,chunks:[]},mc(p.chunks,{src:l,async:!0,integrity:void 0,nonce:e})):(p={src:l.src,chunks:[]},mc(p.chunks,{src:l.src,async:!0,integrity:l.integrity,nonce:e})));f=[];void 0!==d&&(f.push(tb),f.push((""+JSON.stringify(d)).replace(rb,sb)),f.push(ub));
d={placeholderPrefix:x(q+"P:"),segmentPrefix:x(q+"S:"),boundaryPrefix:x(q+"B:"),startInlineScript:n,htmlChunks:null,headChunks:null,externalRuntimeScript:p,bootstrapChunks:t,charsetChunks:[],preconnectChunks:[],importMapChunks:f,preloadChunks:[],hoistableChunks:[],preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:e,
boundaryResources:null,stylesToHoist:!1};if(void 0!==g)for(n=0;n<g.length;n++){var u=g[n];f=p=void 0;l={rel:"preload",as:"script",fetchPriority:"low",nonce:e};"string"===typeof u?l.href=q=u:(l.href=q=u.src,l.integrity=f="string"===typeof u.integrity?u.integrity:void 0,l.crossOrigin=p="string"===typeof u||null==u.crossOrigin?void 0:"use-credentials"===u.crossOrigin?"use-credentials":"");u=c;var E=q;u.scriptResources[E]=null;u.moduleScriptResources[E]=null;u=[];Q(u,l);d.bootstrapScripts.add(u);t.push(lb,
A(q));e&&t.push(nb,A(e));"string"===typeof f&&t.push(ob,A(f));"string"===typeof p&&t.push(pb,A(p));t.push(qb)}if(void 0!==h)for(g=0;g<h.length;g++)l=h[g],p=q=void 0,f={rel:"modulepreload",fetchPriority:"low",nonce:e},"string"===typeof l?f.href=n=l:(f.href=n=l.src,f.integrity=p="string"===typeof l.integrity?l.integrity:void 0,f.crossOrigin=q="string"===typeof l||null==l.crossOrigin?void 0:"use-credentials"===l.crossOrigin?"use-credentials":""),l=c,u=n,l.scriptResources[u]=null,l.moduleScriptResources[u]=
null,l=[],Q(l,f),d.bootstrapScripts.add(l),t.push(mb,A(n)),e&&t.push(nb,A(e)),"string"===typeof p&&t.push(ob,A(p)),"string"===typeof q&&t.push(pb,A(q)),t.push(qb);e=b?b.namespaceURI:void 0;e=B("http://www.w3.org/2000/svg"===e?3:"http://www.w3.org/1998/Math/MathML"===e?4:0,null,0);t=b?b.progressiveChunkSize:void 0;g=b?b.onError:void 0;n=b?b.onAllReady:void 0;q=b?b.onShellReady:void 0;p=b?b.onShellError:void 0;f=b?b.onPostpone:void 0;l=b?b.experimental_formState:void 0;Ka.current=Wa;b=[];h=new Set;
c={destination:null,flushScheduled:!1,resumableState:c,renderState:d,rootFormatContext:e,progressiveChunkSize:void 0===t?12800:t,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:h,pingedTasks:b,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===g?ig:g,onPostpone:void 0===f?jg:f,onAllReady:void 0===n?jg:n,onShellReady:void 0===q?jg:q,onShellError:void 0===p?jg:p,onFatalError:jg,
formState:void 0===l?null:l};d=qg(c,0,null,e,!1,!1);d.parentFlushed=!0;a=og(c,null,a,-1,null,d,h,null,e,ff,null,rf);b.push(a);return c}
exports.renderToPipeableStream=function(a,b){var c=Pg(a,b),d=!1;Kg(c);return{pipe:function(e){if(d)throw Error("React currently only supports piping to one writable stream.");d=!0;Lg(c,e);e.on("drain",Ng(e,c));e.on("error",Og(c,"The destination stream errored while writing data."));e.on("close",Og(c,"The destination stream closed early."));return e},abort:function(e){Mg(c,e)}}};exports.version="18.3.0-canary-1dba980e1f-20241220";
