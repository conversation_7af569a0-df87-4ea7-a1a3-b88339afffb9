/**
 * @license React
 * react-dom-server-legacy.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("next/dist/compiled/react"),fa=require("react-dom");function p(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
var t=Object.assign,w=Object.prototype.hasOwnProperty,ha=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ia={},ta={};
function ua(a){if(w.call(ta,a))return!0;if(w.call(ia,a))return!1;if(ha.test(a))return ta[a]=!0;ia[a]=!0;return!1}
var va=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),wa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),xa=/["'&<>]/;
function x(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=xa.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var ya=/([A-Z])/g,Ca=/^ms-/,Da=Array.isArray,Ea=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Fa=fa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,bb={prefetchDNS:Ga,preconnect:Ha,preload:Ia,preloadModule:Ya,preinitStyle:Za,preinitScript:$a,preinitModuleScript:ab},cb=[];
function db(a,b){var c=0;void 0!==b&&(c=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:c,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function y(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function eb(a,b,c){switch(b){case "noscript":return y(2,null,a.tagScope|1);case "select":return y(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return y(3,null,a.tagScope);case "picture":return y(2,null,a.tagScope|2);case "math":return y(4,null,a.tagScope);case "foreignObject":return y(2,null,a.tagScope);case "table":return y(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return y(6,null,a.tagScope);case "colgroup":return y(8,null,a.tagScope);case "tr":return y(7,null,a.tagScope)}return 5<=
a.insertionMode?y(2,null,a.tagScope):0===a.insertionMode?"html"===b?y(1,null,a.tagScope):y(2,null,a.tagScope):1===a.insertionMode?y(2,null,a.tagScope):a}var fb=new Map;
function gb(a,b){if("object"!==typeof b)throw Error(p(62));var c=!0,d;for(d in b)if(w.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=x(d);e=x((""+e).trim())}else f=fb.get(d),void 0===f&&(f=x(d.replace(ya,"-$1").toLowerCase().replace(Ca,"-ms-")),fb.set(d,f)),e="number"===typeof e?0===e||va.has(d)?""+e:e+"px":x((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}
function hb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function D(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',x(c),'"')}x("javascript:throw new Error('A React form was unexpectedly submitted.')");function ib(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error(p(480));D(this,"name",b);D(this,"value",a);this.push("/>")}
function sb(a,b,c,d,e,f,g,h){null!=h&&E(a,"name",h);null!=d&&E(a,"formAction",d);null!=e&&E(a,"formEncType",e);null!=f&&E(a,"formMethod",f);null!=g&&E(a,"formTarget",g);return null}
function E(a,b,c){switch(b){case "className":D(a,"class",c);break;case "tabIndex":D(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":D(a,b,c);break;case "style":gb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',x(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":hb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',x(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',x(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',x(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',x(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',x(c),'"');break;case "xlinkActuate":D(a,"xlink:actuate",
c);break;case "xlinkArcrole":D(a,"xlink:arcrole",c);break;case "xlinkRole":D(a,"xlink:role",c);break;case "xlinkShow":D(a,"xlink:show",c);break;case "xlinkTitle":D(a,"xlink:title",c);break;case "xlinkType":D(a,"xlink:type",c);break;case "xmlBase":D(a,"xml:base",c);break;case "xmlLang":D(a,"xml:lang",c);break;case "xmlSpace":D(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=wa.get(b)||b,ua(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',x(c),'"')}}}function F(a,b,c){if(null!=b){if(null!=c)throw Error(p(60));if("object"!==typeof b||!("__html"in b))throw Error(p(61));b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}function tb(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function ub(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return I(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return I(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:x(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:t({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&vb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return I(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return I(d.preconnectChunks,b);case "preload":return I(d.preloadChunks,
b);default:return I(d.hoistableChunks,b)}}function I(a,b){a.push(J("link"));for(var c in b)if(w.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error(p(399,"link"));default:E(a,c,d)}}a.push("/>");return null}function wb(a,b,c){a.push(J(c));for(var d in b)if(w.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(p(399,c));default:E(a,d,e)}}a.push("/>");return null}
function xb(a,b){a.push(J("title"));var c=null,d=null,e;for(e in b)if(w.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:E(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(x(""+b));F(a,d,c);a.push("</","title",">");return null}
function yb(a,b){a.push(J("script"));var c=null,d=null,e;for(e in b)if(w.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:E(a,e,f)}}a.push(">");F(a,d,c);"string"===typeof c&&a.push(x(c));a.push("</","script",">");return null}
function zb(a,b,c){a.push(J(c));var d=c=null,e;for(e in b)if(w.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:E(a,e,f)}}a.push(">");F(a,d,c);return"string"===typeof c?(a.push(x(c)),null):c}var Ab=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Bb=new Map;function J(a){var b=Bb.get(a);if(void 0===b){if(!Ab.test(a))throw Error(p(65,a));b="<"+a;Bb.set(a,b)}return b}
function Cb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(J("select"));var h=null,k=null,l;for(l in c)if(w.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:E(a,l,n)}}a.push(">");F(a,k,h);return h;case "option":var r=f.selectedValue;a.push(J("option"));var m=null,u=null,G=null,H=null,v;for(v in c)if(w.call(c,
v)){var q=c[v];if(null!=q)switch(v){case "children":m=q;break;case "selected":G=q;break;case "dangerouslySetInnerHTML":H=q;break;case "value":u=q;default:E(a,v,q)}}if(null!=r){var A=null!==u?""+u:tb(m);if(Da(r))for(var ba=0;ba<r.length;ba++){if(""+r[ba]===A){a.push(' selected=""');break}}else""+r===A&&a.push(' selected=""')}else G&&a.push(' selected=""');a.push(">");F(a,H,m);return m;case "textarea":a.push(J("textarea"));var K=null,Q=null,L=null,z;for(z in c)if(w.call(c,z)){var W=c[z];if(null!=W)switch(z){case "children":L=
W;break;case "value":K=W;break;case "defaultValue":Q=W;break;case "dangerouslySetInnerHTML":throw Error(p(91));default:E(a,z,W)}}null===K&&null!==Q&&(K=Q);a.push(">");if(null!=L){if(null!=K)throw Error(p(92));if(Da(L)&&1<L.length)throw Error(p(93));K=""+L}"string"===typeof K&&"\n"===K[0]&&a.push("\n");null!==K&&a.push(x(""+K));return null;case "input":a.push(J("input"));var T=null,ca=null,M=null,X=null,B=null,Ja=null,Ka=null,La=null,Ma=null,ja;for(ja in c)if(w.call(c,ja)){var R=c[ja];if(null!=R)switch(ja){case "children":case "dangerouslySetInnerHTML":throw Error(p(399,
"input"));case "name":T=R;break;case "formAction":ca=R;break;case "formEncType":M=R;break;case "formMethod":X=R;break;case "formTarget":B=R;break;case "defaultChecked":Ma=R;break;case "defaultValue":Ka=R;break;case "checked":La=R;break;case "value":Ja=R;break;default:E(a,ja,R)}}var jb=sb(a,d,e,ca,M,X,B,T);null!==La?hb(a,"checked",La):null!==Ma&&hb(a,"checked",Ma);null!==Ja?E(a,"value",Ja):null!==Ka&&E(a,"value",Ka);a.push("/>");null!==jb&&jb.forEach(ib,a);return null;case "button":a.push(J("button"));
var Y=null,ka=null,la=null,ma=null,Na=null,na=null,rc=null,Oa;for(Oa in c)if(w.call(c,Oa)){var da=c[Oa];if(null!=da)switch(Oa){case "children":Y=da;break;case "dangerouslySetInnerHTML":ka=da;break;case "name":la=da;break;case "formAction":ma=da;break;case "formEncType":Na=da;break;case "formMethod":na=da;break;case "formTarget":rc=da;break;default:E(a,Oa,da)}}var sc=sb(a,d,e,ma,Na,na,rc,la);a.push(">");null!==sc&&sc.forEach(ib,a);F(a,ka,Y);if("string"===typeof Y){a.push(x(Y));var tc=null}else tc=
Y;return tc;case "form":a.push(J("form"));var Pa=null,uc=null,Lb=null,Mb=null,Nb=null,Ob=null,Qa;for(Qa in c)if(w.call(c,Qa)){var ea=c[Qa];if(null!=ea)switch(Qa){case "children":Pa=ea;break;case "dangerouslySetInnerHTML":uc=ea;break;case "action":Lb=ea;break;case "encType":Mb=ea;break;case "method":Nb=ea;break;case "target":Ob=ea;break;default:E(a,Qa,ea)}}null!=Lb&&E(a,"action",Lb);null!=Mb&&E(a,"encType",Mb);null!=Nb&&E(a,"method",Nb);null!=Ob&&E(a,"target",Ob);a.push(">");F(a,uc,Pa);if("string"===
typeof Pa){a.push(x(Pa));var vc=null}else vc=Pa;return vc;case "menuitem":a.push(J("menuitem"));for(var kb in c)if(w.call(c,kb)){var wc=c[kb];if(null!=wc)switch(kb){case "children":case "dangerouslySetInnerHTML":throw Error(p(400));default:E(a,kb,wc)}}a.push(">");return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var xc=xb(a,c);else xb(e.hoistableChunks,c),xc=null;return xc;case "link":return ub(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Pb=c.async;
if("string"!==typeof c.src||!c.src||!Pb||"function"===typeof Pb||"symbol"===typeof Pb||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var yc=yb(a,c);else{var lb=c.src;if("module"===c.type){var mb=d.moduleScriptResources;var zc=e.preloads.moduleScripts}else mb=d.scriptResources,zc=e.preloads.scripts;var nb=mb.hasOwnProperty(lb)?mb[lb]:void 0;if(null!==nb){mb[lb]=null;var Qb=c;if(nb){2===nb.length&&(Qb=t({},c),vb(Qb,nb));var Ac=zc.get(lb);Ac&&(Ac.length=0)}var Bc=[];e.scripts.add(Bc);
yb(Bc,Qb)}g&&a.push("\x3c!-- --\x3e");yc=null}return yc;case "style":var ob=c.precedence,oa=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof ob||"string"!==typeof oa||""===oa){a.push(J("style"));var za=null,Cc=null,Ra;for(Ra in c)if(w.call(c,Ra)){var pb=c[Ra];if(null!=pb)switch(Ra){case "children":za=pb;break;case "dangerouslySetInnerHTML":Cc=pb;break;default:E(a,Ra,pb)}}a.push(">");var Sa=Array.isArray(za)?2>za.length?za[0]:null:za;"function"!==typeof Sa&&"symbol"!==
typeof Sa&&null!==Sa&&void 0!==Sa&&a.push(x(""+Sa));F(a,Cc,za);a.push("</","style",">");var Dc=null}else{var pa=e.styles.get(ob);if(null!==(d.styleResources.hasOwnProperty(oa)?d.styleResources[oa]:void 0)){d.styleResources[oa]=null;pa?pa.hrefs.push(x(oa)):(pa={precedence:x(ob),rules:[],hrefs:[x(oa)],sheets:new Map},e.styles.set(ob,pa));var Ec=pa.rules,Aa=null,Fc=null,qb;for(qb in c)if(w.call(c,qb)){var Rb=c[qb];if(null!=Rb)switch(qb){case "children":Aa=Rb;break;case "dangerouslySetInnerHTML":Fc=Rb}}var Ta=
Array.isArray(Aa)?2>Aa.length?Aa[0]:null:Aa;"function"!==typeof Ta&&"symbol"!==typeof Ta&&null!==Ta&&void 0!==Ta&&Ec.push(x(""+Ta));F(Ec,Fc,Aa)}pa&&e.boundaryResources&&e.boundaryResources.styles.add(pa);g&&a.push("\x3c!-- --\x3e");Dc=void 0}return Dc;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Gc=wb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),Gc="string"===typeof c.charSet?wb(e.charsetChunks,c,"meta"):"viewport"===c.name?wb(e.preconnectChunks,c,"meta"):wb(e.hoistableChunks,
c,"meta");return Gc;case "listing":case "pre":a.push(J(b));var Ua=null,Va=null,Wa;for(Wa in c)if(w.call(c,Wa)){var rb=c[Wa];if(null!=rb)switch(Wa){case "children":Ua=rb;break;case "dangerouslySetInnerHTML":Va=rb;break;default:E(a,Wa,rb)}}a.push(">");if(null!=Va){if(null!=Ua)throw Error(p(60));if("object"!==typeof Va||!("__html"in Va))throw Error(p(61));var qa=Va.__html;null!==qa&&void 0!==qa&&("string"===typeof qa&&0<qa.length&&"\n"===qa[0]?a.push("\n",qa):a.push(""+qa))}"string"===typeof Ua&&"\n"===
Ua[0]&&a.push("\n");return Ua;case "img":var N=c.src,C=c.srcSet;if(!("lazy"===c.loading||!N&&!C||"string"!==typeof N&&null!=N||"string"!==typeof C&&null!=C)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof C||":"!==C[4]||"d"!==C[0]&&"D"!==C[0]||"a"!==C[1]&&"A"!==C[1]||"t"!==C[2]&&"T"!==C[2]||"a"!==C[3]&&"A"!==C[3])){var Hc="string"===typeof c.sizes?
c.sizes:void 0,Xa=C?C+"\n"+(Hc||""):N,Sb=e.preloads.images,ra=Sb.get(Xa);if(ra){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Sb.delete(Xa),e.highImagePreloads.add(ra)}else d.imageResources.hasOwnProperty(Xa)||(d.imageResources[Xa]=cb,ra=[],I(ra,{rel:"preload",as:"image",href:C?void 0:N,imageSrcSet:C,imageSizes:Hc,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?
e.highImagePreloads.add(ra):(e.bulkPreloads.add(ra),Sb.set(Xa,ra)))}return wb(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return wb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Ic=zb(e.headChunks,
c,"head")}else Ic=zb(a,c,"head");return Ic;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Jc=zb(e.htmlChunks,c,"html")}else Jc=zb(a,c,"html");return Jc;default:if(-1!==b.indexOf("-")){a.push(J(b));var Tb=null,Kc=null,Ba;for(Ba in c)if(w.call(c,Ba)){var sa=c[Ba];if(null!=sa)switch(Ba){case "children":Tb=sa;break;case "dangerouslySetInnerHTML":Kc=sa;break;case "style":gb(a,sa);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:ua(Ba)&&
"function"!==typeof sa&&"symbol"!==typeof sa&&a.push(" ",Ba,'="',x(sa),'"')}}a.push(">");F(a,Kc,Tb);return Tb}}return zb(a,c,b)}function Db(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}function Eb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error(p(395));a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Fb(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error(p(397));}}
function Gb(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error(p(397));}}var Hb=/[<\u2028\u2029]/g;
function Ib(a){return JSON.stringify(a).replace(Hb,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Jb=/[&><\u2028\u2029]/g;
function Kb(a){return JSON.stringify(a).replace(Jb,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Ub=!1,Vb=!0;
function Wb(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);Vb=this.push("</style>");Ub=!0;b.length=0;c.length=0}}function Xb(a){return 2!==a.state?Ub=!0:!1}function Yb(a,b,c){Ub=!1;Vb=!0;b.styles.forEach(Wb,a);b.stylesheets.forEach(Xb);Ub&&(c.stylesToHoist=!0);return Vb}
function O(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var Zb=[];function $b(a){I(Zb,a.props);for(var b=0;b<Zb.length;b++)this.push(Zb[b]);Zb.length=0;a.state=2}
function ac(a){var b=0<a.sheets.size;a.sheets.forEach($b,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function bc(a){if(0===a.state){a.state=1;var b=a.props;I(Zb,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Zb.length;a++)this.push(Zb[a]);Zb.length=0}}function cc(a){a.sheets.forEach(bc,this);a.sheets.clear()}
function dc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=Kb(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=Kb(""+d.props.href);a.push(g);e=""+e;a.push(",");e=Kb(e);a.push(e);for(var h in f)if(w.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(p(399,"link"));default:a:{e=
a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ua(h))break a;g=""+g}e.push(",");k=Kb(k);e.push(k);e.push(",");g=Kb(g);e.push(g)}}a.push("]");
c=",[";d.state=3}});a.push("]")}
function ec(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=x(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=x(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=x(JSON.stringify(e));a.push(e);for(var h in f)if(w.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(p(399,"link"));
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ua(h))break a;g=""+g}e.push(",");k=x(JSON.stringify(k));e.push(k);
e.push(",");g=x(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Ga(a){var b=P?P:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;I(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}fc(b)}}}
function Ha(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;I(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}fc(c)}}}
function Ia(a,b,c){var d=P?P:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=cb;e=[];I(e,t({rel:"preload",href:g?void 0:a,as:b},c));"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];I(g,t({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?cb:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);I(g,t({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?cb:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=t({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}I(e,c);g[a]=cb}fc(d)}}}
function Ya(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?cb:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=cb}I(f,t({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);fc(c)}}}
function Za(a,b,c){var d=P?P:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:x(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:t({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&vb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),fc(d))}}}
function $a(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=t({src:a,async:!0},b),f&&(2===f.length&&vb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),yb(a,b),fc(c))}}}
function ab(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=t({src:a,type:"module",async:!0},b),f&&(2===f.length&&vb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),yb(a,b),fc(c))}}}function vb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function gc(a){this.styles.add(a)}
function hc(a){this.stylesheets.add(a)}
function ic(a,b){var c=a.idPrefix;a=c+"P:";var d=c+"S:";c+="B:";var e=new Set,f=new Set,g=new Set,h=new Map,k=new Set,l=new Set,n=new Set,r={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};return{placeholderPrefix:a,segmentPrefix:d,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:[],charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:e,fontPreloads:f,
highImagePreloads:g,styles:h,bootstrapScripts:k,scripts:l,bulkPreloads:n,preloads:r,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function jc(a,b,c,d){if(c.generateStaticMarkup)return a.push(x(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(x(b)),a=!0);return a}
var kc=Symbol.for("react.element"),lc=Symbol.for("react.portal"),mc=Symbol.for("react.fragment"),nc=Symbol.for("react.strict_mode"),oc=Symbol.for("react.profiler"),pc=Symbol.for("react.provider"),qc=Symbol.for("react.context"),Lc=Symbol.for("react.server_context"),Mc=Symbol.for("react.forward_ref"),Nc=Symbol.for("react.suspense"),Oc=Symbol.for("react.suspense_list"),Pc=Symbol.for("react.memo"),Qc=Symbol.for("react.lazy"),Rc=Symbol.for("react.scope"),Sc=Symbol.for("react.debug_trace_mode"),Tc=Symbol.for("react.offscreen"),
Uc=Symbol.for("react.legacy_hidden"),Vc=Symbol.for("react.cache"),Wc=Symbol.for("react.default_value"),Xc=Symbol.iterator;
function Yc(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case mc:return"Fragment";case lc:return"Portal";case oc:return"Profiler";case nc:return"StrictMode";case Nc:return"Suspense";case Oc:return"SuspenseList";case Vc:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case qc:return(a.displayName||"Context")+".Consumer";case pc:return(a._context.displayName||"Context")+".Provider";case Mc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Pc:return b=a.displayName||null,null!==b?b:Yc(a.type)||"Memo";case Qc:b=a._payload;a=a._init;try{return Yc(a(b))}catch(c){break}case Lc:return(a.displayName||a._globalName)+".Provider"}return null}var Zc={};function $c(a,b){a=a.contextTypes;if(!a)return Zc;var c={},d;for(d in a)c[d]=b[d];return c}var ad=null;
function bd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error(p(401));}else{if(null===c)throw Error(p(401));bd(a,c)}b.context._currentValue2=b.value}}function cd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&cd(a)}function dd(a){var b=a.parent;null!==b&&dd(b);a.context._currentValue2=a.value}
function ed(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error(p(402));a.depth===b.depth?bd(a,b):ed(a,b)}function fd(a,b){var c=b.parent;if(null===c)throw Error(p(402));a.depth===c.depth?bd(a,c):fd(a,c);b.context._currentValue2=b.value}function gd(a){var b=ad;b!==a&&(null===b?dd(a):null===a?cd(b):b.depth===a.depth?bd(b,a):b.depth>a.depth?ed(b,a):fd(b,a),ad=a)}
var hd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function id(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=hd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:t({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&hd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=t({},f,h)):t(f,h))}a.state=f}else f.queue=null}
var jd={id:1,overflow:""};function kd(a,b,c){var d=a.id;a=a.overflow;var e=32-ld(d)-1;d&=~(1<<e);c+=1;var f=32-ld(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-ld(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var ld=Math.clz32?Math.clz32:md,nd=Math.log,od=Math.LN2;function md(a){a>>>=0;return 0===a?32:31-(nd(a)/od|0)|0}var pd=Error(p(460));function qd(){}
function rd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(qd,qd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}sd=b;throw pd;}}var sd=null;
function td(){if(null===sd)throw Error(p(459));var a=sd;sd=null;return a}function ud(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var vd="function"===typeof Object.is?Object.is:ud,S=null,wd=null,xd=null,U=null,yd=!1,zd=!1,Ad=0,Bd=0,Cd=-1,Dd=0,Ed=null,Fd=null,Gd=0;function Hd(){if(null===S)throw Error(p(321));return S}function Id(){if(0<Gd)throw Error(p(312));return{memoizedState:null,queue:null,next:null}}
function Jd(){null===U?null===xd?(yd=!1,xd=U=Id()):(yd=!0,U=xd):null===U.next?(yd=!1,U=U.next=Id()):(yd=!0,U=U.next);return U}function Kd(a,b,c,d){for(;zd;)zd=!1,Bd=Ad=0,Cd=-1,Dd=0,Gd+=1,U=null,c=a(b,d);Ld();return c}function Md(){var a=Ed;Ed=null;return a}function Ld(){wd=S=null;zd=!1;xd=null;Gd=0;U=Fd=null}function Nd(a,b){return"function"===typeof b?b(a):b}
function Od(a,b,c){S=Hd();U=Jd();if(yd){var d=U.queue;b=d.dispatch;if(null!==Fd&&(c=Fd.get(d),void 0!==c)){Fd.delete(d);d=U.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);U.memoizedState=d;return[d,b]}return[U.memoizedState,b]}a=a===Nd?"function"===typeof b?b():b:void 0!==c?c(b):b;U.memoizedState=a;a=U.queue={last:null,dispatch:null};a=a.dispatch=Pd.bind(null,S,a);return[U.memoizedState,a]}
function Qd(a,b){S=Hd();U=Jd();b=void 0===b?null:b;if(null!==U){var c=U.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!vd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();U.memoizedState=[a,b];return a}function Pd(a,b,c){if(25<=Gd)throw Error(p(301));if(a===S)if(zd=!0,a={action:c,next:null},null===Fd&&(Fd=new Map),c=Fd.get(b),void 0===c)Fd.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}
function Rd(){throw Error(p(394));}function Sd(a){var b=Dd;Dd+=1;null===Ed&&(Ed=[]);return rd(Ed,a,b)}function Td(){throw Error(p(393));}function Ud(){}
var Wd={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Sd(a);if(a.$$typeof===qc||a.$$typeof===Lc)return a._currentValue2}throw Error(p(438,String(a)));},useContext:function(a){Hd();return a._currentValue2},useMemo:Qd,useReducer:Od,useRef:function(a){S=Hd();U=Jd();var b=U.memoizedState;return null===b?(a={current:a},U.memoizedState=a):b},useState:function(a){return Od(Nd,a)},useInsertionEffect:Ud,useLayoutEffect:Ud,
useCallback:function(a,b){return Qd(function(){return a},b)},useImperativeHandle:Ud,useEffect:Ud,useDebugValue:Ud,useDeferredValue:function(a){Hd();return a},useTransition:function(){Hd();return[!1,Rd]},useId:function(){var a=wd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-ld(a)-1)).toString(32)+b;var c=Vd;if(null===c)throw Error(p(404));b=Ad++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error(p(407));return c()},useCacheRefresh:function(){return Td}},
Vd=null,Xd={getCacheSignal:function(){throw Error(p(248));},getCacheForType:function(){throw Error(p(248));}},Yd=Ea.ReactCurrentDispatcher,Zd=Ea.ReactCurrentCache;function $d(a){console.error(a);return null}function ae(){}
function be(a,b,c,d,e,f,g,h,k,l,n,r){Fa.current=bb;var m=[],u=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:u,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?$d:f,onPostpone:void 0===n?ae:n,onAllReady:void 0===g?
ae:g,onShellReady:void 0===h?ae:h,onShellError:void 0===k?ae:k,onFatalError:void 0===l?ae:l,formState:void 0===r?null:r};c=ce(b,0,null,d,!1,!1);c.parentFlushed=!0;a=de(b,null,a,-1,null,c,u,null,d,Zc,null,jd);m.push(a);return b}var P=null;function ee(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,fe(a))}
function ge(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function de(a,b,c,d,e,f,g,h,k,l,n,r){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return ee(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:r,thenableState:b};g.add(m);return m}
function he(a,b,c,d,e,f,g,h,k,l,n,r){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return ee(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:r,thenableState:b};g.add(m);return m}function ce(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function V(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function ie(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function je(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error(p(108,Yc(e)||"Unknown",h));e=t({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function ke(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=kd(c,1,0),le(a,b,d,-1),b.treeContext=c):h?le(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function me(a,b){if(a&&a.defaultProps){b=t({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function ne(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=$c(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);id(h,e,f,d);je(a,b,c,h,e)}else{h=$c(e,b.legacyContext);S={};wd=b;Bd=Ad=0;Cd=-1;Dd=0;Ed=d;d=e(f,h);d=Kd(e,f,d,h);g=0!==Ad;var k=Bd,l=Cd;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(id(d,e,f,h),je(a,b,c,d,e)):ke(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=b.blockedSegment,
null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=eb(h,e,f),b.keyPath=c,le(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Cb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=eb(h,e,f);b.keyPath=c;le(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push("</",e,">")}d.lastPushedText=!1}else{switch(e){case Uc:case Sc:case nc:case oc:case mc:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case Tc:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case Oc:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case Rc:throw Error(p(343));case Nc:a:if(null!==
b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{le(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var n=b.blockedSegment;d=f.fallback;var r=f.children;f=new Set;g=ge(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=ce(a,n.chunks.length,g,b.formatContext,!1,!1);n.children.push(k);n.lastPushedText=!1;var m=ce(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=c;try{if(le(a,
b,r,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,oe(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(u){m.status=4,g.status=4,h=V(a,u),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=n,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(n=[h[1],h[2],[],null],l.workingMap.set(h,n),5===g.status?l.workingMap.get(c)[4]=n:g.trackedFallbackNode=
n);b=de(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Mc:e=e.render;S={};wd=b;Bd=Ad=0;Cd=-1;Dd=0;Ed=d;d=e(f,g);f=Kd(e,f,d,g);ke(a,b,c,f,0!==Ad,Bd,Cd);return;case Pc:e=e.type;f=me(e,f);ne(a,b,c,d,e,f,g);return;case pc:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue2;e._currentValue2=f;k=ad;ad=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:g,value:f};b.context=
f;b.keyPath=c;Z(a,b,null,h,-1);a=ad;if(null===a)throw Error(p(403));c=a.parentValue;a.context._currentValue2=c===Wc?a.context._defaultValue:c;a=ad=a.parent;b.context=a;b.keyPath=d;return;case qc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case Qc:h=e._init;e=h(e._payload);f=me(e,f);ne(a,b,c,d,e,f,void 0);return}throw Error(p(130,null==e?e:typeof e,""));}}
function pe(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=ce(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,le(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(oe(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case kc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=Yc(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var r=b.replay;e=r.nodes;for(d=0;d<e.length;d++){var m=e[d];if(n===m[1]){if(null!==l&&l!==m[0])throw Error(p(489,l));if(4===m.length){l=m[2];m=m[3];b.replay={nodes:l,slots:m,pendingTasks:1};try{if("number"===typeof m){n=a;var u=b,G=u.replay,H=u.blockedBoundary,v=ce(n,0,null,u.formatContext,
!1,!1);v.id=m;v.parentFlushed=!0;try{u.replay=null,u.blockedSegment=v,ne(n,u,g,c,f,h,k),v.status=1,null===H?n.completedRootSegment=v:(oe(H,v),H.parentFlushed&&n.partialBoundaries.push(H))}finally{u.replay=G,u.blockedSegment=null}}else ne(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(p(488));}catch(A){if("object"===typeof A&&null!==A&&(A===pd||"function"===typeof A.then))throw A;h=void 0;var q=b.blockedBoundary;g=A;h=V(a,g);qe(a,q,l,m,g,h)}finally{b.replay.pendingTasks--,
b.replay=r}}else{if(f!==Nc)throw Error(p(490));b:{q=void 0;G=m[5];H=m[2];v=m[3];f=null===m[4]?[]:m[4][2];r=null===m[4]?null:m[4][3];k=b.keyPath;l=b.replay;m=b.blockedBoundary;n=h.children;h=h.fallback;c=new Set;u=ge(a,c);u.parentFlushed=!0;u.rootSegmentID=G;b.blockedBoundary=u;b.replay={nodes:H,slots:v,pendingTasks:1};a.renderState.boundaryResources=u.resources;try{"number"===typeof v?pe(a,b,v,n,-1):le(a,b,n,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(p(488));b.replay.pendingTasks--;
if(0===u.pendingTasks&&0===u.status){u.status=1;a.completedBoundaries.push(u);break b}}catch(A){u.status=4,q=V(a,A),u.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(u)}finally{a.renderState.boundaryResources=m?m.resources:null,b.blockedBoundary=m,b.replay=l,b.keyPath=k}g=[g[0],"Suspense Fallback",g[2]];"number"===typeof r?(q=ce(a,0,null,b.formatContext,!1,!1),q.id=r,q.parentFlushed=!0,b=de(a,null,h,-1,m,q,c,g,b.formatContext,b.legacyContext,b.context,b.treeContext)):b=he(a,
null,{nodes:f,slots:r,pendingTasks:0},h,-1,m,c,g,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else ne(a,b,g,c,f,h,k);return;case lc:throw Error(p(257));case Qc:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(Da(d)){re(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=Xc&&d[Xc]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);re(a,b,g,e)}return}if("function"===
typeof d.then)return Z(a,b,null,Sd(d),e);if(d.$$typeof===qc||d.$$typeof===Lc)return Z(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error(p(31,"[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e));}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=jc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=jc(e.chunks,""+d,a.renderState,e.lastPushedText)))}
function re(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{if(re(a,b,c,-1),1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(p(488));}catch(r){if("object"===typeof r&&null!==r&&(r===pd||"function"===typeof r.then))throw r;c=void 0;var l=b.blockedBoundary,n=r;c=V(a,n);qe(a,l,d,k,n,c)}finally{b.replay.pendingTasks--,
b.replay=f}g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(l=0;l<g;l++)d=c[l],b.treeContext=kd(f,g,l),k=h[l],"number"===typeof k?(pe(a,b,k,d,l),delete h[l]):le(a,b,d,l);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)l=c[h],b.treeContext=kd(f,g,h),le(a,b,l,h);b.treeContext=f;b.keyPath=e}
function le(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return Z(a,b,null,c,d)}catch(m){if(Ld(),c=m===pd?td():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Md();a=he(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;gd(g);return}}else{var n=
l.children.length,r=l.chunks.length;try{return Z(a,b,null,c,d)}catch(m){if(Ld(),l.children.length=n,l.chunks.length=r,c=m===pd?td():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Md();l=b.blockedSegment;n=ce(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(n);l.lastPushedText=!1;a=de(a,d,b.node,b.childIndex,b.blockedBoundary,n,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;gd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;gd(g);throw c;}function se(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,te(this,b,a))}
function qe(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)qe(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=ge(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error(p(487));4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var r in d)delete d[r]}}
function ue(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);null===d?(b.allPendingTasks--,1!==b.status&&2!==b.status&&(a=a.replay,null===a?(V(b,c),ie(b,c)):(a.pendingTasks--,0===a.pendingTasks&&0<a.nodes.length&&(d=V(b,c),qe(b,null,a.nodes,a.slots,c,d))))):(d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=V(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return ue(f,b,c)}),d.fallbackAbortableTasks.clear(),b.allPendingTasks--,
0===b.allPendingTasks&&(a=b.onAllReady,a()))}function oe(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&oe(a,c)}else a.completedSegments.push(b)}
function te(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error(p(389));a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=ae,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&oe(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(se,a),b.fallbackAbortableTasks.clear())):null!==
c&&c.parentFlushed&&1===c.status&&(oe(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function fe(a){if(2!==a.status){var b=ad,c=Yd.current;Yd.current=Wd;var d=Zd.current;Zd.current=Xd;var e=P;P=a;var f=Vd;Vd=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var r=k.blockedSegment;if(null===r){var m=l;if(0!==k.replay.pendingTasks){gd(k.context);try{var u=k.thenableState;k.thenableState=null;Z(m,k,u,k.node,-1);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error(p(488));
k.replay.pendingTasks--;k.abortSet.delete(k);te(m,k.blockedBoundary,null)}catch(B){Ld();var G=B===pd?td():B;if("object"===typeof G&&null!==G&&"function"===typeof G.then){var H=k.ping;G.then(H,H);k.thenableState=Md()}else{k.replay.pendingTasks--;k.abortSet.delete(k);l=void 0;var v=m,q=k.blockedBoundary,A=G,ba=k.replay.nodes,K=k.replay.slots;l=V(v,A);qe(v,q,ba,K,A,l);m.allPendingTasks--;if(0===m.allPendingTasks){var Q=m.onAllReady;Q()}}}finally{m.renderState.boundaryResources=null}}}else if(m=void 0,
v=r,0===v.status){gd(k.context);var L=v.children.length,z=v.chunks.length;try{var W=k.thenableState;k.thenableState=null;Z(l,k,W,k.node,k.childIndex);l.renderState.generateStaticMarkup||v.lastPushedText&&v.textEmbedded&&v.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);v.status=1;te(l,k.blockedBoundary,v)}catch(B){Ld();v.children.length=L;v.chunks.length=z;var T=B===pd?td():B;if("object"===typeof T&&null!==T&&"function"===typeof T.then){var ca=k.ping;T.then(ca,ca);k.thenableState=Md()}else{k.abortSet.delete(k);
v.status=4;var M=k.blockedBoundary;m=V(l,T);null===M?ie(l,T):(M.pendingTasks--,4!==M.status&&(M.status=4,M.errorDigest=m,M.parentFlushed&&l.clientRenderedBoundaries.push(M)));l.allPendingTasks--;if(0===l.allPendingTasks){var X=l.onAllReady;X()}}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&ve(a,a.destination)}catch(B){V(a,B),ie(a,B)}finally{Vd=f,Yd.current=c,Zd.current=d,c===Wd&&gd(b),P=e}}}
function we(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=xe(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error(p(390));
}}
function xe(a,b,c){var d=c.boundary;if(null===d)return we(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=x(d),b.push(d),b.push('"')),b.push("></template>")),we(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Eb(b,a.renderState,
d.rootSegmentID),we(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Eb(b,a.renderState,d.rootSegmentID),we(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(gc,e),c.stylesheets.forEach(hc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error(p(391));xe(a,b,c[0]);a=a.renderState.generateStaticMarkup?
!0:b.push("\x3c!--/$--\x3e");return a}function ye(a,b,c){Fb(b,a.renderState,c.parentFormatContext,c.id);xe(a,b,c);return Gb(b,c.parentFormatContext)}
function ze(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Ae(a,b,c,d[e]);d.length=0;Yb(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),dc(b,c)):(b.push('" data-sty="'),ec(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Db(b,a)&&d}
function Ae(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error(p(392));return ye(a,b,d)}if(e===c.rootSegmentID)return ye(a,b,d);ye(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function ve(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var r=J("head");b.push(r);b.push(">")}}else if(n)for(f=
0;f<n.length;f++)b.push(n[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(O,b);e.preconnects.clear();var u=e.preconnectChunks;for(f=0;f<u.length;f++)b.push(u[f]);u.length=0;e.fontPreloads.forEach(O,b);e.fontPreloads.clear();e.highImagePreloads.forEach(O,b);e.highImagePreloads.clear();e.styles.forEach(ac,b);var G=e.importMapChunks;for(f=0;f<G.length;f++)b.push(G[f]);G.length=0;e.bootstrapScripts.forEach(O,b);e.scripts.forEach(O,b);e.scripts.clear();e.bulkPreloads.forEach(O,
b);e.bulkPreloads.clear();var H=e.preloadChunks;for(f=0;f<H.length;f++)b.push(H[f]);H.length=0;var v=e.hoistableChunks;for(f=0;f<v.length;f++)b.push(v[f]);v.length=0;l&&null===n&&(b.push("</"),b.push("head"),b.push(">"));xe(a,b,d);a.completedRootSegment=null;Db(b,a.renderState)}else return;var q=a.renderState;d=0;q.preconnects.forEach(O,b);q.preconnects.clear();var A=q.preconnectChunks;for(d=0;d<A.length;d++)b.push(A[d]);A.length=0;q.fontPreloads.forEach(O,b);q.fontPreloads.clear();q.highImagePreloads.forEach(O,
b);q.highImagePreloads.clear();q.styles.forEach(cc,b);q.scripts.forEach(O,b);q.scripts.clear();q.bulkPreloads.forEach(O,b);q.bulkPreloads.clear();var ba=q.preloadChunks;for(d=0;d<ba.length;d++)b.push(ba[d]);ba.length=0;var K=q.hoistableChunks;for(d=0;d<K.length;d++)b.push(K[d]);K.length=0;var Q=a.clientRenderedBoundaries;for(c=0;c<Q.length;c++){var L=Q[c];q=b;var z=a.resumableState,W=a.renderState,T=L.rootSegmentID,ca=L.errorDigest,M=L.errorMessage,X=L.errorComponentStack,B=0===z.streamingFormat;
B?(q.push(W.startInlineScript),0===(z.instructions&4)?(z.instructions|=4,q.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):q.push('$RX("')):q.push('<template data-rxi="" data-bid="');q.push(W.boundaryPrefix);var Ja=T.toString(16);q.push(Ja);B&&q.push('"');if(ca||M||X)if(B){q.push(",");var Ka=Ib(ca||"");q.push(Ka)}else{q.push('" data-dgst="');var La=x(ca||
"");q.push(La)}if(M||X)if(B){q.push(",");var Ma=Ib(M||"");q.push(Ma)}else{q.push('" data-msg="');var ja=x(M||"");q.push(ja)}if(X)if(B){q.push(",");var R=Ib(X);q.push(R)}else{q.push('" data-stck="');var jb=x(X);q.push(jb)}if(B?!q.push(")\x3c/script>"):!q.push('"></template>')){a.destination=null;c++;Q.splice(0,c);return}}Q.splice(0,c);var Y=a.completedBoundaries;for(c=0;c<Y.length;c++)if(!ze(a,b,Y[c])){a.destination=null;c++;Y.splice(0,c);return}Y.splice(0,c);var ka=a.partialBoundaries;for(c=0;c<ka.length;c++){var la=
ka[c];a:{Q=a;L=b;Q.renderState.boundaryResources=la.resources;var ma=la.completedSegments;for(z=0;z<ma.length;z++)if(!Ae(Q,L,la,ma[z])){z++;ma.splice(0,z);var Na=!1;break a}ma.splice(0,z);Na=Yb(L,la.resources,Q.renderState)}if(!Na){a.destination=null;c++;ka.splice(0,c);return}}ka.splice(0,c);var na=a.completedBoundaries;for(c=0;c<na.length;c++)if(!ze(a,b,na[c])){a.destination=null;c++;na.splice(0,c);return}na.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&
0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(b.push("</"),b.push("body"),b.push(">")),c.hasHtml&&(b.push("</"),b.push("html"),b.push(">")),b.push(null),a.destination=null)}}function fc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?ve(a,b):a.flushScheduled=!1}}
function Be(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error(p(432)):b;c.forEach(function(e){return ue(e,a,d)});c.clear()}null!==a.destination&&ve(a,a.destination)}catch(e){V(a,e),ie(a,e)}}function Ce(){}
function De(a,b,c,d){var e=!1,f=null,g="",h={push:function(l){null!==l&&(g+=l);return!0},destroy:function(l){e=!0;f=l}},k=!1;b=db(b?b.identifierPrefix:void 0,void 0);a=be(a,b,ic(b,c),y(0,null,0),Infinity,Ce,void 0,function(){k=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;fe(a);Be(a,d);if(1===a.status)a.status=2,h.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=h;try{ve(a,h)}catch(l){V(a,l),ie(a,l)}}if(e&&f!==d)throw f;if(!k)throw Error(p(426));return g}
exports.renderToNodeStream=function(){throw Error(p(207));};exports.renderToStaticMarkup=function(a,b){return De(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};exports.renderToStaticNodeStream=function(){throw Error(p(208));};exports.renderToString=function(a,b){return De(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-1dba980e1f-20241220";
