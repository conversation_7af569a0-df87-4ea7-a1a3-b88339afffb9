/**
 * @license React
 * react-dom-server-legacy.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var ea=require("next/dist/compiled/react"),fa=require("react-dom"),ha=require("stream"),r=Object.assign,v=Object.prototype.hasOwnProperty,sa=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),
ta={},ua={};function va(a){if(v.call(ua,a))return!0;if(v.call(ta,a))return!1;if(sa.test(a))return ua[a]=!0;ta[a]=!0;return!1}
var wa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),xa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ba=/["'&<>]/;
function w(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ba.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ca=/([A-Z])/g,Da=/^ms-/,Ea=Array.isArray,Fa=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ga=fa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,cb={prefetchDNS:Ha,preconnect:Xa,preload:Ya,preloadModule:Za,preinitStyle:$a,preinitScript:ab,preinitModuleScript:bb},db=[];
function eb(a,b){var c=0;void 0!==b&&(c=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:c,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function x(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function fb(a){return x("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function gb(a,b,c){switch(b){case "noscript":return x(2,null,a.tagScope|1);case "select":return x(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return x(3,null,a.tagScope);case "picture":return x(2,null,a.tagScope|2);case "math":return x(4,null,a.tagScope);case "foreignObject":return x(2,null,a.tagScope);case "table":return x(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return x(6,null,a.tagScope);case "colgroup":return x(8,null,a.tagScope);case "tr":return x(7,null,a.tagScope)}return 5<=
a.insertionMode?x(2,null,a.tagScope):0===a.insertionMode?"html"===b?x(1,null,a.tagScope):x(2,null,a.tagScope):1===a.insertionMode?x(2,null,a.tagScope):a}var hb=new Map;
function rb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(v.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=w(d);e=w((""+e).trim())}else f=hb.get(d),void 0===f&&(f=w(d.replace(Ca,"-$1").toLowerCase().replace(Da,"-ms-")),hb.set(d,f)),e="number"===typeof e?0===e||wa.has(d)?""+e:e+"px":
w((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function sb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function C(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',w(c),'"')}w("javascript:throw new Error('A React form was unexpectedly submitted.')");
function tb(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");C(this,"name",b);C(this,"value",a);this.push("/>")}function ub(a,b,c,d,e,f,g,h){null!=h&&D(a,"name",h);null!=d&&D(a,"formAction",d);null!=e&&D(a,"formEncType",e);null!=f&&D(a,"formMethod",f);null!=g&&D(a,"formTarget",g);return null}
function D(a,b,c){switch(b){case "className":C(a,"class",c);break;case "tabIndex":C(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":C(a,b,c);break;case "style":rb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',w(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":sb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',w(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',w(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',w(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',w(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',w(c),'"');break;case "xlinkActuate":C(a,"xlink:actuate",
c);break;case "xlinkArcrole":C(a,"xlink:arcrole",c);break;case "xlinkRole":C(a,"xlink:role",c);break;case "xlinkShow":C(a,"xlink:show",c);break;case "xlinkTitle":C(a,"xlink:title",c);break;case "xlinkType":C(a,"xlink:type",c);break;case "xmlBase":C(a,"xml:base",c);break;case "xmlLang":C(a,"xml:lang",c);break;case "xmlSpace":C(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=xa.get(b)||b,va(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',w(c),'"')}}}function E(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function vb(a){var b="";ea.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function wb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return H(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return H(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:w(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:r({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&xb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return H(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return H(d.preconnectChunks,b);case "preload":return H(d.preloadChunks,
b);default:return H(d.hoistableChunks,b)}}function H(a,b){a.push(I("link"));for(var c in b)if(v.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:D(a,c,d)}}a.push("/>");return null}
function yb(a,b,c){a.push(I(c));for(var d in b)if(v.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:D(a,d,e)}}a.push("/>");return null}
function zb(a,b){a.push(I("title"));var c=null,d=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(w(""+b));E(a,d,c);a.push("</","title",">");return null}
function Ab(a,b){a.push(I("script"));var c=null,d=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");E(a,d,c);"string"===typeof c&&a.push(w(c));a.push("</","script",">");return null}
function Bb(a,b,c){a.push(I(c));var d=c=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");E(a,d,c);return"string"===typeof c?(a.push(w(c)),null):c}var Cb=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Db=new Map;function I(a){var b=Db.get(a);if(void 0===b){if(!Cb.test(a))throw Error("Invalid tag: "+a);b="<"+a;Db.set(a,b)}return b}
function Eb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(I("select"));var h=null,k=null,l;for(l in c)if(v.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:D(a,l,n)}}a.push(">");E(a,k,h);return h;case "option":var q=f.selectedValue;a.push(I("option"));var m=null,t=null,F=null,G=null,u;for(u in c)if(v.call(c,
u)){var p=c[u];if(null!=p)switch(u){case "children":m=p;break;case "selected":F=p;break;case "dangerouslySetInnerHTML":G=p;break;case "value":t=p;default:D(a,u,p)}}if(null!=q){var z=null!==t?""+t:vb(m);if(Ea(q))for(var aa=0;aa<q.length;aa++){if(""+q[aa]===z){a.push(' selected=""');break}}else""+q===z&&a.push(' selected=""')}else F&&a.push(' selected=""');a.push(">");E(a,G,m);return m;case "textarea":a.push(I("textarea"));var J=null,P=null,K=null,y;for(y in c)if(v.call(c,y)){var V=c[y];if(null!=V)switch(y){case "children":K=
V;break;case "value":J=V;break;case "defaultValue":P=V;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:D(a,y,V)}}null===J&&null!==P&&(J=P);a.push(">");if(null!=K){if(null!=J)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ea(K)&&1<K.length)throw Error("<textarea> can only have at most one child.");J=""+K}"string"===typeof J&&"\n"===J[0]&&a.push("\n");null!==J&&a.push(w(""+J));return null;
case "input":a.push(I("input"));var S=null,ba=null,L=null,W=null,A=null,Ia=null,Ja=null,Ka=null,La=null,ia;for(ia in c)if(v.call(c,ia)){var Q=c[ia];if(null!=Q)switch(ia){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":S=Q;break;case "formAction":ba=Q;break;case "formEncType":L=Q;break;case "formMethod":W=Q;break;case "formTarget":A=Q;break;case "defaultChecked":La=Q;break;case "defaultValue":Ja=
Q;break;case "checked":Ka=Q;break;case "value":Ia=Q;break;default:D(a,ia,Q)}}var ib=ub(a,d,e,ba,L,W,A,S);null!==Ka?sb(a,"checked",Ka):null!==La&&sb(a,"checked",La);null!==Ia?D(a,"value",Ia):null!==Ja&&D(a,"value",Ja);a.push("/>");null!==ib&&ib.forEach(tb,a);return null;case "button":a.push(I("button"));var X=null,ja=null,ka=null,la=null,Ma=null,ma=null,uc=null,Na;for(Na in c)if(v.call(c,Na)){var ca=c[Na];if(null!=ca)switch(Na){case "children":X=ca;break;case "dangerouslySetInnerHTML":ja=ca;break;
case "name":ka=ca;break;case "formAction":la=ca;break;case "formEncType":Ma=ca;break;case "formMethod":ma=ca;break;case "formTarget":uc=ca;break;default:D(a,Na,ca)}}var vc=ub(a,d,e,la,Ma,ma,uc,ka);a.push(">");null!==vc&&vc.forEach(tb,a);E(a,ja,X);if("string"===typeof X){a.push(w(X));var wc=null}else wc=X;return wc;case "form":a.push(I("form"));var Oa=null,xc=null,Kb=null,Lb=null,Mb=null,Nb=null,Pa;for(Pa in c)if(v.call(c,Pa)){var da=c[Pa];if(null!=da)switch(Pa){case "children":Oa=da;break;case "dangerouslySetInnerHTML":xc=
da;break;case "action":Kb=da;break;case "encType":Lb=da;break;case "method":Mb=da;break;case "target":Nb=da;break;default:D(a,Pa,da)}}null!=Kb&&D(a,"action",Kb);null!=Lb&&D(a,"encType",Lb);null!=Mb&&D(a,"method",Mb);null!=Nb&&D(a,"target",Nb);a.push(">");E(a,xc,Oa);if("string"===typeof Oa){a.push(w(Oa));var yc=null}else yc=Oa;return yc;case "menuitem":a.push(I("menuitem"));for(var jb in c)if(v.call(c,jb)){var zc=c[jb];if(null!=zc)switch(jb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");
default:D(a,jb,zc)}}a.push(">");return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Ac=zb(a,c);else zb(e.hoistableChunks,c),Ac=null;return Ac;case "link":return wb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Ob=c.async;if("string"!==typeof c.src||!c.src||!Ob||"function"===typeof Ob||"symbol"===typeof Ob||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Bc=Ab(a,c);else{var kb=c.src;if("module"===c.type){var lb=d.moduleScriptResources;
var Cc=e.preloads.moduleScripts}else lb=d.scriptResources,Cc=e.preloads.scripts;var mb=lb.hasOwnProperty(kb)?lb[kb]:void 0;if(null!==mb){lb[kb]=null;var Pb=c;if(mb){2===mb.length&&(Pb=r({},c),xb(Pb,mb));var Dc=Cc.get(kb);Dc&&(Dc.length=0)}var Ec=[];e.scripts.add(Ec);Ab(Ec,Pb)}g&&a.push("\x3c!-- --\x3e");Bc=null}return Bc;case "style":var nb=c.precedence,na=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof nb||"string"!==typeof na||""===na){a.push(I("style"));var ya=
null,Fc=null,Qa;for(Qa in c)if(v.call(c,Qa)){var ob=c[Qa];if(null!=ob)switch(Qa){case "children":ya=ob;break;case "dangerouslySetInnerHTML":Fc=ob;break;default:D(a,Qa,ob)}}a.push(">");var Ra=Array.isArray(ya)?2>ya.length?ya[0]:null:ya;"function"!==typeof Ra&&"symbol"!==typeof Ra&&null!==Ra&&void 0!==Ra&&a.push(w(""+Ra));E(a,Fc,ya);a.push("</","style",">");var Gc=null}else{var oa=e.styles.get(nb);if(null!==(d.styleResources.hasOwnProperty(na)?d.styleResources[na]:void 0)){d.styleResources[na]=null;
oa?oa.hrefs.push(w(na)):(oa={precedence:w(nb),rules:[],hrefs:[w(na)],sheets:new Map},e.styles.set(nb,oa));var Hc=oa.rules,za=null,Ic=null,pb;for(pb in c)if(v.call(c,pb)){var Qb=c[pb];if(null!=Qb)switch(pb){case "children":za=Qb;break;case "dangerouslySetInnerHTML":Ic=Qb}}var Sa=Array.isArray(za)?2>za.length?za[0]:null:za;"function"!==typeof Sa&&"symbol"!==typeof Sa&&null!==Sa&&void 0!==Sa&&Hc.push(w(""+Sa));E(Hc,Ic,za)}oa&&e.boundaryResources&&e.boundaryResources.styles.add(oa);g&&a.push("\x3c!-- --\x3e");
Gc=void 0}return Gc;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Jc=yb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),Jc="string"===typeof c.charSet?yb(e.charsetChunks,c,"meta"):"viewport"===c.name?yb(e.preconnectChunks,c,"meta"):yb(e.hoistableChunks,c,"meta");return Jc;case "listing":case "pre":a.push(I(b));var Ta=null,Ua=null,Va;for(Va in c)if(v.call(c,Va)){var qb=c[Va];if(null!=qb)switch(Va){case "children":Ta=qb;break;case "dangerouslySetInnerHTML":Ua=qb;break;default:D(a,
Va,qb)}}a.push(">");if(null!=Ua){if(null!=Ta)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof Ua||!("__html"in Ua))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var pa=Ua.__html;null!==pa&&void 0!==pa&&("string"===typeof pa&&0<pa.length&&"\n"===pa[0]?a.push("\n",pa):a.push(""+pa))}"string"===typeof Ta&&"\n"===Ta[0]&&a.push("\n");
return Ta;case "img":var M=c.src,B=c.srcSet;if(!("lazy"===c.loading||!M&&!B||"string"!==typeof M&&null!=M||"string"!==typeof B&&null!=B)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof M||":"!==M[4]||"d"!==M[0]&&"D"!==M[0]||"a"!==M[1]&&"A"!==M[1]||"t"!==M[2]&&"T"!==M[2]||"a"!==M[3]&&"A"!==M[3])&&("string"!==typeof B||":"!==B[4]||"d"!==B[0]&&"D"!==B[0]||"a"!==B[1]&&"A"!==B[1]||"t"!==B[2]&&"T"!==B[2]||"a"!==B[3]&&"A"!==B[3])){var Kc="string"===typeof c.sizes?c.sizes:void 0,Wa=B?
B+"\n"+(Kc||""):M,Rb=e.preloads.images,qa=Rb.get(Wa);if(qa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Rb.delete(Wa),e.highImagePreloads.add(qa)}else d.imageResources.hasOwnProperty(Wa)||(d.imageResources[Wa]=db,qa=[],H(qa,{rel:"preload",as:"image",href:B?void 0:M,imageSrcSet:B,imageSizes:Kc,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(qa):
(e.bulkPreloads.add(qa),Rb.set(Wa,qa)))}return yb(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return yb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Lc=Bb(e.headChunks,c,"head")}else Lc=Bb(a,
c,"head");return Lc;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Mc=Bb(e.htmlChunks,c,"html")}else Mc=Bb(a,c,"html");return Mc;default:if(-1!==b.indexOf("-")){a.push(I(b));var Sb=null,Nc=null,Aa;for(Aa in c)if(v.call(c,Aa)){var ra=c[Aa];if(null!=ra)switch(Aa){case "children":Sb=ra;break;case "dangerouslySetInnerHTML":Nc=ra;break;case "style":rb(a,ra);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:va(Aa)&&"function"!==typeof ra&&
"symbol"!==typeof ra&&a.push(" ",Aa,'="',w(ra),'"')}}a.push(">");E(a,Nc,Sb);return Sb}}return Bb(a,c,b)}function Fb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}function Gb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Hb(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function Ib(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var Jb=/[<\u2028\u2029]/g;
function Tb(a){return JSON.stringify(a).replace(Jb,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Ub=/[&><\u2028\u2029]/g;
function Vb(a){return JSON.stringify(a).replace(Ub,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Wb=!1,Xb=!0;
function Yb(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);Xb=this.push("</style>");Wb=!0;b.length=0;c.length=0}}function Zb(a){return 2!==a.state?Wb=!0:!1}function $b(a,b,c){Wb=!1;Xb=!0;b.styles.forEach(Yb,a);b.stylesheets.forEach(Zb);Wb&&(c.stylesToHoist=!0);return Xb}
function N(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var ac=[];function bc(a){H(ac,a.props);for(var b=0;b<ac.length;b++)this.push(ac[b]);ac.length=0;a.state=2}
function cc(a){var b=0<a.sheets.size;a.sheets.forEach(bc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function dc(a){if(0===a.state){a.state=1;var b=a.props;H(ac,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<ac.length;a++)this.push(ac[a]);ac.length=0}}function ec(a){a.sheets.forEach(dc,this);a.sheets.clear()}
function fc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=Vb(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=Vb(""+d.props.href);a.push(g);e=""+e;a.push(",");e=Vb(e);a.push(e);for(var h in f)if(v.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!va(h))break a;g=""+g}e.push(",");k=Vb(k);e.push(k);e.push(",");g=
Vb(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function gc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=w(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=w(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=w(JSON.stringify(e));a.push(e);for(var h in f)if(v.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!va(h))break a;g=""+g}e.push(",");k=w(JSON.stringify(k));e.push(k);
e.push(",");g=w(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Ha(a){var b=O?O:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;H(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}hc(b)}}}
function Xa(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;H(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}hc(c)}}}
function Ya(a,b,c){var d=O?O:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=db;e=[];H(e,r({rel:"preload",href:g?void 0:a,as:b},c));"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];H(g,r({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?db:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);H(g,r({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?db:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=r({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}H(e,c);g[a]=db}hc(d)}}}
function Za(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?db:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=db}H(f,r({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);hc(c)}}}
function $a(a,b,c){var d=O?O:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:w(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:r({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&xb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),hc(d))}}}
function ab(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=r({src:a,async:!0},b),f&&(2===f.length&&xb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Ab(a,b),hc(c))}}}
function bb(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=r({src:a,type:"module",async:!0},b),f&&(2===f.length&&xb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Ab(a,b),hc(c))}}}function xb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function ic(a){this.styles.add(a)}
function jc(a){this.stylesheets.add(a)}
function kc(a,b){var c=a.idPrefix;a=c+"P:";var d=c+"S:";c+="B:";var e=new Set,f=new Set,g=new Set,h=new Map,k=new Set,l=new Set,n=new Set,q={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};return{placeholderPrefix:a,segmentPrefix:d,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:[],charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:e,fontPreloads:f,
highImagePreloads:g,styles:h,bootstrapScripts:k,scripts:l,bulkPreloads:n,preloads:q,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function lc(a,b,c,d){if(c.generateStaticMarkup)return a.push(w(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(w(b)),a=!0);return a}
var mc=Symbol.for("react.element"),nc=Symbol.for("react.portal"),oc=Symbol.for("react.fragment"),pc=Symbol.for("react.strict_mode"),qc=Symbol.for("react.profiler"),rc=Symbol.for("react.provider"),sc=Symbol.for("react.context"),tc=Symbol.for("react.server_context"),Oc=Symbol.for("react.forward_ref"),Pc=Symbol.for("react.suspense"),Qc=Symbol.for("react.suspense_list"),Rc=Symbol.for("react.memo"),Sc=Symbol.for("react.lazy"),Tc=Symbol.for("react.scope"),Uc=Symbol.for("react.debug_trace_mode"),Vc=Symbol.for("react.offscreen"),
Wc=Symbol.for("react.legacy_hidden"),Xc=Symbol.for("react.cache"),Yc=Symbol.for("react.default_value"),Zc=Symbol.iterator;
function $c(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case oc:return"Fragment";case nc:return"Portal";case qc:return"Profiler";case pc:return"StrictMode";case Pc:return"Suspense";case Qc:return"SuspenseList";case Xc:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case sc:return(a.displayName||"Context")+".Consumer";case rc:return(a._context.displayName||"Context")+".Provider";case Oc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Rc:return b=a.displayName||null,null!==b?b:$c(a.type)||"Memo";case Sc:b=a._payload;a=a._init;try{return $c(a(b))}catch(c){break}case tc:return(a.displayName||a._globalName)+".Provider"}return null}var ad={};function bd(a,b){a=a.contextTypes;if(!a)return ad;var c={},d;for(d in a)c[d]=b[d];return c}var cd=null;
function dd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");dd(a,c)}b.context._currentValue2=b.value}}function ed(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&ed(a)}
function fd(a){var b=a.parent;null!==b&&fd(b);a.context._currentValue2=a.value}function gd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?dd(a,b):gd(a,b)}
function hd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?dd(a,c):hd(a,c);b.context._currentValue2=b.value}function id(a){var b=cd;b!==a&&(null===b?fd(a):null===a?ed(b):b.depth===a.depth?dd(b,a):b.depth>a.depth?gd(b,a):hd(b,a),cd=a)}
var jd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function kd(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=jd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:r({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&jd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=r({},f,h)):r(f,h))}a.state=f}else f.queue=null}
var ld={id:1,overflow:""};function md(a,b,c){var d=a.id;a=a.overflow;var e=32-nd(d)-1;d&=~(1<<e);c+=1;var f=32-nd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-nd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var nd=Math.clz32?Math.clz32:od,pd=Math.log,qd=Math.LN2;function od(a){a>>>=0;return 0===a?32:31-(pd(a)/qd|0)|0}var rd=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function sd(){}function td(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(sd,sd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}ud=b;throw rd;}}var ud=null;
function vd(){if(null===ud)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=ud;ud=null;return a}function wd(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var xd="function"===typeof Object.is?Object.is:wd,R=null,yd=null,zd=null,T=null,Ad=!1,Bd=!1,Cd=0,Dd=0,Ed=-1,Fd=0,Gd=null,Hd=null,Id=0;
function Jd(){if(null===R)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return R}
function Kd(){if(0<Id)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function Ld(){null===T?null===zd?(Ad=!1,zd=T=Kd()):(Ad=!0,T=zd):null===T.next?(Ad=!1,T=T.next=Kd()):(Ad=!0,T=T.next);return T}function Md(a,b,c,d){for(;Bd;)Bd=!1,Dd=Cd=0,Ed=-1,Fd=0,Id+=1,T=null,c=a(b,d);Nd();return c}function Od(){var a=Gd;Gd=null;return a}function Nd(){yd=R=null;Bd=!1;zd=null;Id=0;T=Hd=null}function Pd(a,b){return"function"===typeof b?b(a):b}
function Qd(a,b,c){R=Jd();T=Ld();if(Ad){var d=T.queue;b=d.dispatch;if(null!==Hd&&(c=Hd.get(d),void 0!==c)){Hd.delete(d);d=T.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);T.memoizedState=d;return[d,b]}return[T.memoizedState,b]}a=a===Pd?"function"===typeof b?b():b:void 0!==c?c(b):b;T.memoizedState=a;a=T.queue={last:null,dispatch:null};a=a.dispatch=Rd.bind(null,R,a);return[T.memoizedState,a]}
function Sd(a,b){R=Jd();T=Ld();b=void 0===b?null:b;if(null!==T){var c=T.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!xd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();T.memoizedState=[a,b];return a}
function Rd(a,b,c){if(25<=Id)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===R)if(Bd=!0,a={action:c,next:null},null===Hd&&(Hd=new Map),c=Hd.get(b),void 0===c)Hd.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Td(){throw Error("startTransition cannot be called during server rendering.");}function Ud(a){var b=Fd;Fd+=1;null===Gd&&(Gd=[]);return td(Gd,a,b)}
function Vd(){throw Error("Cache cannot be refreshed during server rendering.");}function Wd(){}
var Yd={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Ud(a);if(a.$$typeof===sc||a.$$typeof===tc)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){Jd();return a._currentValue2},useMemo:Sd,useReducer:Qd,useRef:function(a){R=Jd();T=Ld();var b=T.memoizedState;return null===b?(a={current:a},T.memoizedState=a):b},useState:function(a){return Qd(Pd,a)},
useInsertionEffect:Wd,useLayoutEffect:Wd,useCallback:function(a,b){return Sd(function(){return a},b)},useImperativeHandle:Wd,useEffect:Wd,useDebugValue:Wd,useDeferredValue:function(a){Jd();return a},useTransition:function(){Jd();return[!1,Td]},useId:function(){var a=yd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-nd(a)-1)).toString(32)+b;var c=Xd;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Cd++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Vd}},Xd=null,Zd={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},$d=Fa.ReactCurrentDispatcher,ae=Fa.ReactCurrentCache;function be(a){console.error(a);return null}
function ce(){}
function de(a,b,c,d,e,f,g,h,k,l,n,q){Ga.current=cb;var m=[],t=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:t,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?be:f,onPostpone:void 0===n?ce:n,onAllReady:void 0===g?
ce:g,onShellReady:void 0===h?ce:h,onShellError:void 0===k?ce:k,onFatalError:void 0===l?ce:l,formState:void 0===q?null:q};c=ee(b,0,null,d,!1,!1);c.parentFlushed=!0;a=fe(b,null,a,-1,null,c,t,null,d,ad,null,ld);m.push(a);return b}var O=null;function ge(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,he(a))}
function ie(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function fe(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return ge(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}
function je(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return ge(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}function ee(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function U(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function ke(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function le(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error(($c(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=r({},c,d)}b.legacyContext=e;Y(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Y(a,b,null,f,-1),b.keyPath=e}
function me(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=md(c,1,0),Z(a,b,d,-1),b.treeContext=c):h?Z(a,b,d,-1):Y(a,b,null,d,-1);b.keyPath=f}function ne(a,b){if(a&&a.defaultProps){b=r({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function oe(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=bd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);kd(h,e,f,d);le(a,b,c,h,e)}else{h=bd(e,b.legacyContext);R={};yd=b;Dd=Cd=0;Ed=-1;Fd=0;Gd=d;d=e(f,h);d=Md(e,f,d,h);g=0!==Cd;var k=Dd,l=Ed;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(kd(d,e,f,h),le(a,b,c,d,e)):me(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=b.blockedSegment,
null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=gb(h,e,f),b.keyPath=c,Z(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Eb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=gb(h,e,f);b.keyPath=c;Z(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push("</",e,">")}d.lastPushedText=!1}else{switch(e){case Wc:case Uc:case pc:case qc:case oc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case Vc:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Y(a,b,null,f.children,-1),b.keyPath=e);return;case Qc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case Tc:throw Error("ReactDOMServer does not yet support scope components.");
case Pc:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Z(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var n=b.blockedSegment;d=f.fallback;var q=f.children;f=new Set;g=ie(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=ee(a,n.chunks.length,g,b.formatContext,!1,!1);n.children.push(k);n.lastPushedText=!1;var m=ee(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Z(a,b,q,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,pe(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(t){m.status=4,g.status=4,h=U(a,t),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=n,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(n=[h[1],h[2],[],null],l.workingMap.set(h,n),5===g.status?l.workingMap.get(c)[4]=
n:g.trackedFallbackNode=n);b=fe(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Oc:e=e.render;R={};yd=b;Dd=Cd=0;Ed=-1;Fd=0;Gd=d;d=e(f,g);f=Md(e,f,d,g);me(a,b,c,f,0!==Cd,Dd,Ed);return;case Rc:e=e.type;f=ne(e,f);oe(a,b,c,d,e,f,g);return;case rc:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue2;e._currentValue2=f;k=cd;cd=f={parent:k,depth:null===k?0:k.depth+1,context:e,
parentValue:g,value:f};b.context=f;b.keyPath=c;Y(a,b,null,h,-1);a=cd;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===Yc?a.context._defaultValue:c;a=cd=a.parent;b.context=a;b.keyPath=d;return;case sc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Y(a,b,null,f,-1);b.keyPath=e;return;case Sc:h=e._init;e=h(e._payload);f=ne(e,f);oe(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function qe(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=ee(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Z(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(pe(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Y(a,b,c,d,e){b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case mc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=$c(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var m=e[d];if(n===m[1]){if(null!==l&&l!==m[0])throw Error('Expected to see a component of type "'+l+"\" in this slot. The tree doesn't match so React will fallback to client rendering.");if(4===m.length){l=m[2];m=m[3];b.replay={nodes:l,
slots:m,pendingTasks:1};try{if("number"===typeof m){n=a;var t=b,F=t.replay,G=t.blockedBoundary,u=ee(n,0,null,t.formatContext,!1,!1);u.id=m;u.parentFlushed=!0;try{t.replay=null,t.blockedSegment=u,oe(n,t,g,c,f,h,k),u.status=1,null===G?n.completedRootSegment=u:(pe(G,u),G.parentFlushed&&n.partialBoundaries.push(G))}finally{t.replay=F,t.blockedSegment=null}}else oe(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
}catch(z){if("object"===typeof z&&null!==z&&(z===rd||"function"===typeof z.then))throw z;h=void 0;var p=b.blockedBoundary;g=z;h=U(a,g);re(a,p,l,m,g,h)}finally{b.replay.pendingTasks--,b.replay=q}}else{if(f!==Pc)throw Error("Expected to see a Suspense boundary in this slot. The tree doesn't match so React will fallback to client rendering.");b:{p=void 0;F=m[5];G=m[2];u=m[3];f=null===m[4]?[]:m[4][2];q=null===m[4]?null:m[4][3];k=b.keyPath;l=b.replay;m=b.blockedBoundary;n=h.children;h=h.fallback;c=new Set;
t=ie(a,c);t.parentFlushed=!0;t.rootSegmentID=F;b.blockedBoundary=t;b.replay={nodes:G,slots:u,pendingTasks:1};a.renderState.boundaryResources=t.resources;try{"number"===typeof u?qe(a,b,u,n,-1):Z(a,b,n,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===t.pendingTasks&&0===t.status){t.status=1;a.completedBoundaries.push(t);
break b}}catch(z){t.status=4,p=U(a,z),t.errorDigest=p,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(t)}finally{a.renderState.boundaryResources=m?m.resources:null,b.blockedBoundary=m,b.replay=l,b.keyPath=k}g=[g[0],"Suspense Fallback",g[2]];"number"===typeof q?(p=ee(a,0,null,b.formatContext,!1,!1),p.id=q,p.parentFlushed=!0,b=fe(a,null,h,-1,m,p,c,g,b.formatContext,b.legacyContext,b.context,b.treeContext)):b=je(a,null,{nodes:f,slots:q,pendingTasks:0},h,-1,m,c,g,b.formatContext,b.legacyContext,
b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else oe(a,b,g,c,f,h,k);return;case nc:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case Sc:h=d._init;d=h(d._payload);Y(a,b,null,d,e);return}if(Ea(d)){se(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=Zc&&d[Zc]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),
d=h.next();while(!d.done);se(a,b,g,e)}return}if("function"===typeof d.then)return Y(a,b,null,Ud(d),e);if(d.$$typeof===sc||d.$$typeof===tc)return Y(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=lc(e.chunks,
d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=lc(e.chunks,""+d,a.renderState,e.lastPushedText)))}
function se(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{if(se(a,b,c,-1),1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");}catch(q){if("object"===typeof q&&null!==q&&(q===rd||
"function"===typeof q.then))throw q;c=void 0;var l=b.blockedBoundary,n=q;c=U(a,n);re(a,l,d,k,n,c)}finally{b.replay.pendingTasks--,b.replay=f}g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(l=0;l<g;l++)d=c[l],b.treeContext=md(f,g,l),k=h[l],"number"===typeof k?(qe(a,b,k,d,l),delete h[l]):Z(a,b,d,l);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)l=c[h],b.treeContext=md(f,g,h),Z(a,b,l,h);b.treeContext=f;b.keyPath=
e}
function Z(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return Y(a,b,null,c,d)}catch(m){if(Nd(),c=m===rd?vd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Od();a=je(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;id(g);return}}else{var n=l.children.length,
q=l.chunks.length;try{return Y(a,b,null,c,d)}catch(m){if(Nd(),l.children.length=n,l.chunks.length=q,c=m===rd?vd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Od();l=b.blockedSegment;n=ee(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(n);l.lastPushedText=!1;a=fe(a,d,b.node,b.childIndex,b.blockedBoundary,n,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=
h;b.treeContext=k;id(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;id(g);throw c;}function te(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,ue(this,b,a))}
function re(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)re(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=ie(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function ve(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);null===d?(b.allPendingTasks--,1!==b.status&&2!==b.status&&(a=a.replay,null===a?(U(b,c),ke(b,c)):(a.pendingTasks--,0===a.pendingTasks&&0<a.nodes.length&&(d=U(b,c),re(b,null,a.nodes,a.slots,c,d))))):(d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=U(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return ve(f,b,c)}),d.fallbackAbortableTasks.clear(),b.allPendingTasks--,
0===b.allPendingTasks&&(a=b.onAllReady,a()))}function pe(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&pe(a,c)}else a.completedSegments.push(b)}
function ue(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=ce,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&pe(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(te,
a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&(pe(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function he(a){if(2!==a.status){var b=cd,c=$d.current;$d.current=Yd;var d=ae.current;ae.current=Zd;var e=O;O=a;var f=Xd;Xd=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var q=k.blockedSegment;if(null===q){var m=l;if(0!==k.replay.pendingTasks){id(k.context);try{var t=k.thenableState;k.thenableState=null;Y(m,k,t,k.node,-1);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);ue(m,k.blockedBoundary,null)}catch(A){Nd();var F=A===rd?vd():A;if("object"===typeof F&&null!==F&&"function"===typeof F.then){var G=k.ping;F.then(G,G);k.thenableState=Od()}else{k.replay.pendingTasks--;k.abortSet.delete(k);l=void 0;var u=m,p=k.blockedBoundary,z=F,aa=k.replay.nodes,J=k.replay.slots;l=U(u,z);re(u,p,aa,J,z,l);m.allPendingTasks--;if(0===m.allPendingTasks){var P=m.onAllReady;P()}}}finally{m.renderState.boundaryResources=null}}}else if(m=void 0,
u=q,0===u.status){id(k.context);var K=u.children.length,y=u.chunks.length;try{var V=k.thenableState;k.thenableState=null;Y(l,k,V,k.node,k.childIndex);l.renderState.generateStaticMarkup||u.lastPushedText&&u.textEmbedded&&u.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);u.status=1;ue(l,k.blockedBoundary,u)}catch(A){Nd();u.children.length=K;u.chunks.length=y;var S=A===rd?vd():A;if("object"===typeof S&&null!==S&&"function"===typeof S.then){var ba=k.ping;S.then(ba,ba);k.thenableState=Od()}else{k.abortSet.delete(k);
u.status=4;var L=k.blockedBoundary;m=U(l,S);null===L?ke(l,S):(L.pendingTasks--,4!==L.status&&(L.status=4,L.errorDigest=m,L.parentFlushed&&l.clientRenderedBoundaries.push(L)));l.allPendingTasks--;if(0===l.allPendingTasks){var W=l.onAllReady;W()}}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&we(a,a.destination)}catch(A){U(a,A),ke(a,A)}finally{Xd=f,$d.current=c,ae.current=d,c===Yd&&id(b),O=e}}}
function xe(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=ye(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function ye(a,b,c){var d=c.boundary;if(null===d)return xe(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=w(d),b.push(d),b.push('"')),b.push("></template>")),xe(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Gb(b,a.renderState,
d.rootSegmentID),xe(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Gb(b,a.renderState,d.rootSegmentID),xe(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(ic,e),c.stylesheets.forEach(jc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
ye(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function ze(a,b,c){Hb(b,a.renderState,c.parentFormatContext,c.id);ye(a,b,c);return Ib(b,c.parentFormatContext)}
function Ae(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Be(a,b,c,d[e]);d.length=0;$b(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),fc(b,c)):(b.push('" data-sty="'),gc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Fb(b,a)&&d}
function Be(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return ze(a,b,d)}if(e===c.rootSegmentID)return ze(a,b,d);ze(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function we(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var q=I("head");b.push(q);b.push(">")}}else if(n)for(f=
0;f<n.length;f++)b.push(n[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(N,b);e.preconnects.clear();var t=e.preconnectChunks;for(f=0;f<t.length;f++)b.push(t[f]);t.length=0;e.fontPreloads.forEach(N,b);e.fontPreloads.clear();e.highImagePreloads.forEach(N,b);e.highImagePreloads.clear();e.styles.forEach(cc,b);var F=e.importMapChunks;for(f=0;f<F.length;f++)b.push(F[f]);F.length=0;e.bootstrapScripts.forEach(N,b);e.scripts.forEach(N,b);e.scripts.clear();e.bulkPreloads.forEach(N,
b);e.bulkPreloads.clear();var G=e.preloadChunks;for(f=0;f<G.length;f++)b.push(G[f]);G.length=0;var u=e.hoistableChunks;for(f=0;f<u.length;f++)b.push(u[f]);u.length=0;l&&null===n&&(b.push("</"),b.push("head"),b.push(">"));ye(a,b,d);a.completedRootSegment=null;Fb(b,a.renderState)}else return;var p=a.renderState;d=0;p.preconnects.forEach(N,b);p.preconnects.clear();var z=p.preconnectChunks;for(d=0;d<z.length;d++)b.push(z[d]);z.length=0;p.fontPreloads.forEach(N,b);p.fontPreloads.clear();p.highImagePreloads.forEach(N,
b);p.highImagePreloads.clear();p.styles.forEach(ec,b);p.scripts.forEach(N,b);p.scripts.clear();p.bulkPreloads.forEach(N,b);p.bulkPreloads.clear();var aa=p.preloadChunks;for(d=0;d<aa.length;d++)b.push(aa[d]);aa.length=0;var J=p.hoistableChunks;for(d=0;d<J.length;d++)b.push(J[d]);J.length=0;var P=a.clientRenderedBoundaries;for(c=0;c<P.length;c++){var K=P[c];p=b;var y=a.resumableState,V=a.renderState,S=K.rootSegmentID,ba=K.errorDigest,L=K.errorMessage,W=K.errorComponentStack,A=0===y.streamingFormat;
A?(p.push(V.startInlineScript),0===(y.instructions&4)?(y.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(V.boundaryPrefix);var Ia=S.toString(16);p.push(Ia);A&&p.push('"');if(ba||L||W)if(A){p.push(",");var Ja=Tb(ba||"");p.push(Ja)}else{p.push('" data-dgst="');var Ka=w(ba||
"");p.push(Ka)}if(L||W)if(A){p.push(",");var La=Tb(L||"");p.push(La)}else{p.push('" data-msg="');var ia=w(L||"");p.push(ia)}if(W)if(A){p.push(",");var Q=Tb(W);p.push(Q)}else{p.push('" data-stck="');var ib=w(W);p.push(ib)}if(A?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=null;c++;P.splice(0,c);return}}P.splice(0,c);var X=a.completedBoundaries;for(c=0;c<X.length;c++)if(!Ae(a,b,X[c])){a.destination=null;c++;X.splice(0,c);return}X.splice(0,c);var ja=a.partialBoundaries;for(c=0;c<ja.length;c++){var ka=
ja[c];a:{P=a;K=b;P.renderState.boundaryResources=ka.resources;var la=ka.completedSegments;for(y=0;y<la.length;y++)if(!Be(P,K,ka,la[y])){y++;la.splice(0,y);var Ma=!1;break a}la.splice(0,y);Ma=$b(K,ka.resources,P.renderState)}if(!Ma){a.destination=null;c++;ja.splice(0,c);return}}ja.splice(0,c);var ma=a.completedBoundaries;for(c=0;c<ma.length;c++)if(!Ae(a,b,ma[c])){a.destination=null;c++;ma.splice(0,c);return}ma.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&
0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(b.push("</"),b.push("body"),b.push(">")),c.hasHtml&&(b.push("</"),b.push("html"),b.push(">")),b.push(null),a.destination=null)}}function hc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?we(a,b):a.flushScheduled=!1}}
function Ce(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{we(a,b)}catch(c){U(a,c),ke(a,c)}}}function De(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return ve(e,a,d)});c.clear()}null!==a.destination&&we(a,a.destination)}catch(e){U(a,e),ke(a,e)}}function Ee(){}
function Fe(a,b,c,d){var e=!1,f=null,g="",h=!1;b=eb(b?b.identifierPrefix:void 0,void 0);a=de(a,b,kc(b,c),fb(),Infinity,Ee,void 0,function(){h=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;he(a);De(a,d);Ce(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");
return g}function Ge(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var He=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}Ge(b,a);var c=b.prototype;c._destroy=function(d,e){De(this.request);e(d)};c._read=function(){this.startedFlowing&&Ce(this.request,this)};return b}(ha.Readable);function Ie(){}
function Je(a,b){var c=new He;b=eb(b?b.identifierPrefix:void 0,void 0);var d=de(a,b,kc(b,!1),fb(),Infinity,Ie,function(){c.startedFlowing=!0;Ce(d,c)},void 0,void 0,void 0);c.request=d;d.flushScheduled=null!==d.destination;he(d);return c}exports.renderToNodeStream=function(a,b){return Je(a,b)};exports.renderToStaticMarkup=function(a,b){return Fe(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return Je(a,b)};exports.renderToString=function(a,b){return Fe(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-1dba980e1f-20241220";
