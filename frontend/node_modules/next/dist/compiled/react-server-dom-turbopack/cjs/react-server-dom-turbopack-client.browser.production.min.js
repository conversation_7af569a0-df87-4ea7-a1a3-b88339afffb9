/**
 * @license React
 * react-server-dom-turbopack-client.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var r=require("react-dom"),t=require("react"),u={stream:!0};function v(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var w=new Map;
function x(a){var b=__turbopack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function z(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var k=b[d],l=w.get(k);if(void 0===l){l=__turbopack_load__(k);c.push(l);var n=w.set.bind(w,k,null);l.then(n,y);w.set(k,l)}else null!==l&&c.push(l)}return 4===a.length?0===c.length?x(a[0]):Promise.all(c).then(function(){return x(a[0])}):0<c.length?Promise.all(c):null}
var A=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),C=Symbol.for("react.provider"),E=Symbol.for("react.server_context"),aa=Symbol.for("react.lazy"),F=Symbol.for("react.default_value"),G=Symbol.iterator;function ba(a){if(null===a||"object"!==typeof a)return null;a=G&&a[G]||a["@@iterator"];return"function"===typeof a?a:null}var ca=Array.isArray,I=new WeakMap;
function da(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ea(a,b,c,d){function k(m,e){if(null===e)return null;if("object"===typeof e){if("function"===typeof e.then){null===g&&(g=new FormData);n++;var h=l++;e.then(function(p){p=JSON.stringify(p,k);var q=g;q.append(b+h,p);n--;0===n&&c(q)},function(p){d(p)});return"$@"+h.toString(16)}if(e instanceof FormData){null===g&&(g=new FormData);var f=g;m=l++;var D=b+m+"_";e.forEach(function(p,q){f.append(D+q,p)});return"$K"+m.toString(16)}return e instanceof Map?(e=JSON.stringify(Array.from(e),k),null===g&&
(g=new FormData),m=l++,g.append(b+m,e),"$Q"+m.toString(16)):e instanceof Set?(e=JSON.stringify(Array.from(e),k),null===g&&(g=new FormData),m=l++,g.append(b+m,e),"$W"+m.toString(16)):!ca(e)&&ba(e)?Array.from(e):e}if("string"===typeof e){if("Z"===e[e.length-1]&&this[m]instanceof Date)return"$D"+e;e="$"===e[0]?"$"+e:e;return e}if("boolean"===typeof e)return e;if("number"===typeof e)return da(e);if("undefined"===typeof e)return"$undefined";if("function"===typeof e){e=I.get(e);if(void 0!==e)return e=JSON.stringify(e,
k),null===g&&(g=new FormData),m=l++,g.set(b+m,e),"$F"+m.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof e){m=e.description;if(Symbol.for(m)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(e.description+") cannot be found among global symbols."));return"$S"+m}if("bigint"===typeof e)return"$n"+
e.toString(10);throw Error("Type "+typeof e+" is not supported as an argument to a Server Function.");}var l=1,n=0,g=null;a=JSON.stringify(a,k);null===g?c(a):(g.set(b+"0",a),0===n&&c(g))}function J(a,b){I.set(a,b)}var K=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function L(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}L.prototype=Object.create(Promise.prototype);
L.prototype.then=function(a,b){switch(this.status){case "resolved_model":M(this);break;case "resolved_module":N(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function fa(a){switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function O(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function P(a,b,c){switch(a.status){case "fulfilled":O(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&O(c,a.reason)}}
function R(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&O(c,b)}}function S(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(N(a),P(a,c,d))}}var T=null,U=null;
function M(a){var b=T,c=U;T=a;U=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==U&&0<U.deps?(U.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(k){a.status="rejected",a.reason=k}finally{T=b,U=c}}
function N(a){try{var b=a.value,c=__turbopack_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(k){a.status="rejected",a.reason=k}}function V(a,b){a._chunks.forEach(function(c){"pending"===c.status&&R(c,b)})}function W(a,b){var c=a._chunks,d=c.get(b);d||(d=new L("pending",null,null,a),c.set(b,d));return d}
function ha(a,b,c){if(U){var d=U;d.deps++}else d=U={deps:1,value:null};return function(k){b[c]=k;d.deps--;0===d.deps&&"blocked"===a.status&&(k=a.value,a.status="fulfilled",a.value=d.value,null!==k&&O(k,d.value))}}function ia(a){return function(b){return R(a,b)}}
function ja(a,b){function c(){var k=Array.prototype.slice.call(arguments),l=b.bound;return l?"fulfilled"===l.status?d(b.id,l.value.concat(k)):Promise.resolve(l).then(function(n){return d(b.id,n.concat(k))}):d(b.id,k)}var d=a._callServer;J(c,b);return c}function X(a,b){a=W(a,b);switch(a.status){case "resolved_model":M(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ka(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=W(a,b),{$$typeof:aa,_payload:a,_init:fa};case "@":return b=parseInt(d.slice(2),16),W(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),K[a]||(b={$$typeof:E,_currentValue:F,_currentValue2:F,_defaultValue:F,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:C,_context:b},K[a]=b),K[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=X(a,b),ja(a,b);case "Q":return b=parseInt(d.slice(2),16),a=X(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=X(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=W(a,d);switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":return d=T,a.then(ha(d,b,c),ia(d)),null;default:throw a.reason;}}}return d}function la(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function Y(a,b,c,d){var k=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==c?c:la,_nonce:d,_chunks:k,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=ma(a);return a}
function na(a,b,c){var d=a._chunks,k=d.get(b);c=JSON.parse(c,a._fromJSON);var l=v(a._bundlerConfig,c);if(c=z(l)){if(k){var n=k;n.status="blocked"}else n=new L("blocked",null,null,a),d.set(b,n);c.then(function(){return S(n,l)},function(g){return R(n,g)})}else k?S(k,l):d.set(b,new L("resolved_module",l,null,a))}
function ma(a){return function(b,c){return"string"===typeof c?ka(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function Z(a,b){function c(l){var n=l.value;if(l.done)V(a,Error("Connection closed."));else{var g=0,m=a._rowState,e=a._rowID,h=a._rowTag,f=a._rowLength;l=a._buffer;for(var D=n.length;g<D;){var p=-1;switch(m){case 0:p=n[g++];58===p?m=1:e=e<<4|(96<p?p-87:p-48);continue;case 1:m=n[g];84===m?(h=m,m=2,g++):64<m&&91>m?(h=m,m=3,g++):(h=0,m=3);continue;case 2:p=n[g++];44===p?m=4:f=f<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,g);break;case 4:p=g+f,p>n.length&&(p=-1)}var q=n.byteOffset+g;if(-1<p){g=
new Uint8Array(n.buffer,q,p-g);f=a;q=h;var Q=f._stringDecoder;h="";for(var H=0;H<l.length;H++)h+=Q.decode(l[H],u);h+=Q.decode(g);switch(q){case 73:na(f,e,h);break;case 72:e=h[0];h=h.slice(1);f=JSON.parse(h,f._fromJSON);if(h=A.current)switch(e){case "D":h.prefetchDNS(f);break;case "C":"string"===typeof f?h.preconnect(f):h.preconnect(f[0],f[1]);break;case "L":e=f[0];g=f[1];3===f.length?h.preload(e,g,f[2]):h.preload(e,g);break;case "m":"string"===typeof f?h.preloadModule(f):h.preloadModule(f[0],f[1]);
break;case "S":"string"===typeof f?h.preinitStyle(f):h.preinitStyle(f[0],0===f[1]?void 0:f[1],3===f.length?f[2]:void 0);break;case "X":"string"===typeof f?h.preinitScript(f):h.preinitScript(f[0],f[1]);break;case "M":"string"===typeof f?h.preinitModuleScript(f):h.preinitModuleScript(f[0],f[1])}break;case 69:h=JSON.parse(h);g=h.digest;h=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
h.stack="Error: "+h.message;h.digest=g;g=f._chunks;(q=g.get(e))?R(q,h):g.set(e,new L("rejected",null,h,f));break;case 84:f._chunks.set(e,new L("fulfilled",h,null,f));break;default:g=f._chunks,(q=g.get(e))?(f=q,e=h,"pending"===f.status&&(h=f.value,g=f.reason,f.status="resolved_model",f.value=e,null!==h&&(M(f),P(f,h,g)))):g.set(e,new L("resolved_model",h,null,f))}g=p;3===m&&g++;f=e=h=m=0;l.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-g);l.push(n);f-=n.byteLength;break}}a._rowState=m;a._rowID=
e;a._rowTag=h;a._rowLength=f;return k.read().then(c).catch(d)}}function d(l){V(a,l)}var k=b.getReader();k.read().then(c).catch(d)}exports.createFromFetch=function(a,b){var c=Y(null,null,b&&b.callServer?b.callServer:void 0,void 0);a.then(function(d){Z(c,d.body)},function(d){V(c,d)});return W(c,0)};exports.createFromReadableStream=function(a,b){b=Y(null,null,b&&b.callServer?b.callServer:void 0,void 0);Z(b,a);return W(b,0)};
exports.createServerReference=function(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}J(c,{id:a,bound:null});return c};exports.encodeReply=function(a){return new Promise(function(b,c){ea(a,"",b,c)})};
