{"version": 3, "sources": ["../../../src/client/dev/dev-build-watcher.ts"], "names": ["initializeBuildWatcher", "toggleCallback", "position", "shadowHost", "document", "createElement", "verticalProperty", "horizontalProperty", "split", "id", "style", "width", "height", "zIndex", "body", "append<PERSON><PERSON><PERSON>", "shadowRoot", "prefix", "attachShadow", "mode", "container", "createContainer", "css", "createCss", "isVisible", "isBuilding", "timeoutId", "addMessageListener", "obj", "handleMessage", "show", "clearTimeout", "updateContainer", "hide", "setTimeout", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "BUILT", "SYNC", "classList", "add", "remove", "innerHTML", "textContent"], "mappings": "AAAA,0DAA0D;;;;+BAa1D;;;eAAwBA;;;kCAZoB;2BAET;AAUpB,SAASA,uBACtBC,cAAmD,EACnDC,QAAyB;IAAzBA,IAAAA,qBAAAA,WAAW;IAEX,MAAMC,aAAaC,SAASC,aAAa,CAAC;IAC1C,MAAM,CAACC,kBAAkBC,mBAAmB,GAAGL,SAASM,KAAK,CAAC;IAI9DL,WAAWM,EAAE,GAAG;IAChB,gEAAgE;IAChEN,WAAWO,KAAK,CAACR,QAAQ,GAAG;IAC5B,4DAA4D;IAC5DC,WAAWO,KAAK,CAACJ,iBAAiB,GAAG;IACrC,4DAA4D;IAC5DH,WAAWO,KAAK,CAACH,mBAAmB,GAAG;IACvCJ,WAAWO,KAAK,CAACC,KAAK,GAAG;IACzBR,WAAWO,KAAK,CAACE,MAAM,GAAG;IAC1BT,WAAWO,KAAK,CAACG,MAAM,GAAG;IAC1BT,SAASU,IAAI,CAACC,WAAW,CAACZ;IAE1B,IAAIa;IACJ,IAAIC,SAAS;IAEb,IAAId,WAAWe,YAAY,EAAE;QAC3BF,aAAab,WAAWe,YAAY,CAAC;YAAEC,MAAM;QAAO;IACtD,OAAO;QACL,iEAAiE;QACjE,2DAA2D;QAC3D,uBAAuB;QACvBH,aAAab;QACbc,SAAS;IACX;IAEA,YAAY;IACZ,MAAMG,YAAYC,gBAAgBJ;IAClCD,WAAWD,WAAW,CAACK;IAEvB,MAAM;IACN,MAAME,MAAMC,UAAUN,QAAQ;QAAEV;QAAoBD;IAAiB;IACrEU,WAAWD,WAAW,CAACO;IAEvB,QAAQ;IACR,IAAIE,YAAY;IAChB,IAAIC,aAAa;IACjB,IAAIC,YAAkD;IAEtD,gBAAgB;IAEhBC,IAAAA,6BAAkB,EAAC,CAACC;QAClB,IAAI;YACFC,cAAcD;QAChB,EAAE,UAAM,CAAC;IACX;IAEA,SAASE;QACPJ,aAAaK,aAAaL;QAC1BF,YAAY;QACZC,aAAa;QACbO;IACF;IAEA,SAASC;QACPR,aAAa;QACb,+CAA+C;QAC/CC,YAAYQ,WAAW;YACrBV,YAAY;YACZQ;QACF,GAAG;QACHA;IACF;IAEA,SAASH,cAAcD,GAAqB;QAC1C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;YACtB;QACF;QAEA,wCAAwC;QACxC,OAAQA,IAAIO,MAAM;YAChB,KAAKC,6CAA2B,CAACC,QAAQ;gBACvCP;gBACA;YACF,KAAKM,6CAA2B,CAACE,KAAK;YACtC,KAAKF,6CAA2B,CAACG,IAAI;gBACnCN;gBACA;QACJ;IACF;IAEAhC,eAAe;QACb6B;QACAG;IACF;IAEA,SAASD;QACP,IAAIP,YAAY;YACdL,UAAUoB,SAAS,CAACC,GAAG,CAAC,AAAC,KAAExB,SAAO;QACpC,OAAO;YACLG,UAAUoB,SAAS,CAACE,MAAM,CAAC,AAAC,KAAEzB,SAAO;QACvC;QAEA,IAAIO,WAAW;YACbJ,UAAUoB,SAAS,CAACC,GAAG,CAAC,AAAC,KAAExB,SAAO;QACpC,OAAO;YACLG,UAAUoB,SAAS,CAACE,MAAM,CAAC,AAAC,KAAEzB,SAAO;QACvC;IACF;AACF;AAEA,SAASI,gBAAgBJ,MAAc;IACrC,MAAMG,YAAYhB,SAASC,aAAa,CAAC;IACzCe,UAAUX,EAAE,GAAG,AAAC,KAAEQ,SAAO;IACzBG,UAAUuB,SAAS,GAAG,AAAC,oBACV1B,SAAO,oOAQJA,SAAO,iNAMRA,SAAO,0CAAuCA,SAAO;IAOpE,OAAOG;AACT;AAEA,SAASG,UACPN,MAAc,EACd,KAG2D;IAH3D,IAAA,EACEV,kBAAkB,EAClBD,gBAAgB,EACyC,GAH3D;IAKA,MAAMgB,MAAMlB,SAASC,aAAa,CAAC;IACnCiB,IAAIsB,WAAW,GAAG,AAAC,YACd3B,SAAO,mDAENX,mBAAiB,oBACjBC,qBAAmB,weAkBYD,mBAAiB,mCACrCW,SAAO,8CAGnBA,SAAO,eAAYA,SAAO,oDAI1BA,SAAO,eAAYA,SAAO,uBACzBX,mBAAiB,+CAIlBW,SAAO,4EAKPA,SAAO,kFAKPA,SAAO,oCACKA,SAAO,uEAGTA,SAAO,sCAEdX,mBAAiB,gEAIjBA,mBAAiB,oEAKVW,SAAO;IAWtB,OAAOK;AACT"}