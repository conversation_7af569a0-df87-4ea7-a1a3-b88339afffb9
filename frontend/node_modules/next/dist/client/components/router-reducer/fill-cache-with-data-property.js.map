{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-data-property.ts"], "names": ["fillCacheWithDataProperty", "newCache", "existingCache", "flightSegmentPath", "fetchResponse", "bailOnParallelRoutes", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "size", "bailOptimistic", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "CacheStates", "DATA_FETCH", "subTreeData", "slice"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;+CAPY;sCAES;AAK9B,SAASA,0BACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC,EACpCC,aAA8D,EAC9DC,oBAAqC;IAArCA,IAAAA,iCAAAA,uBAAgC;IAEhC,MAAMC,cAAcH,kBAAkBI,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGN;IACpC,MAAMO,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJV,cAAcW,cAAc,CAACC,GAAG,CAACN;IAEnC,IACE,CAACI,2BACAP,wBAAwBH,cAAcW,cAAc,CAACE,IAAI,GAAG,GAC7D;QACA,6EAA6E;QAC7E,qDAAqD;QACrD,sEAAsE;QACtE,OAAO;YAAEC,gBAAgB;QAAK;IAChC;IAEA,IAAIC,kBAAkBhB,SAASY,cAAc,CAACC,GAAG,CAACN;IAElD,IAAI,CAACS,mBAAmBA,oBAAoBL,yBAAyB;QACnEK,kBAAkB,IAAIC,IAAIN;QAC1BX,SAASY,cAAc,CAACM,GAAG,CAACX,kBAAkBS;IAChD;IAEA,MAAMG,yBAAyBR,wBAAwBE,GAAG,CAACJ;IAC3D,IAAIW,iBAAiBJ,gBAAgBH,GAAG,CAACJ;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACe,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACT,UAAU;gBAC5Ba,QAAQC,0CAAW,CAACC,UAAU;gBAC9BH,MAAMlB;gBACNsB,aAAa;gBACbb,gBAAgB,IAAIK;YACtB;QACF;QACA;IACF;IAEA,IAAI,CAACG,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACT,UAAU;gBAC5Ba,QAAQC,0CAAW,CAACC,UAAU;gBAC9BH,MAAMlB;gBACNsB,aAAa;gBACbb,gBAAgB,IAAIK;YACtB;QACF;QACA;IACF;IAEA,IAAIG,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBI,aAAaL,eAAeK,WAAW;YACvCb,gBAAgB,IAAIK,IAAIG,eAAeR,cAAc;QACvD;QACAI,gBAAgBE,GAAG,CAACT,UAAUW;IAChC;IAEA,OAAOrB,0BACLqB,gBACAD,wBACAjB,kBAAkBwB,KAAK,CAAC,IACxBvB;AAEJ"}