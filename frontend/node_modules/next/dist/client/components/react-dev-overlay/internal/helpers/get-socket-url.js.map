{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/get-socket-url.ts"], "names": ["getSocketUrl", "getSocketProtocol", "assetPrefix", "protocol", "window", "location", "URL", "hostname", "port", "normalizedAssetPrefix", "replace", "url", "startsWith", "split"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;AAXhB,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,OAAOC,QAAQ,CAACF,QAAQ;IAEvC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIG,IAAIJ,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,OAAO;AACvC;AAEO,SAASH,aAAaE,WAAmB;IAC9C,MAAM,EAAEK,QAAQ,EAAEC,IAAI,EAAE,GAAGJ,OAAOC,QAAQ;IAC1C,MAAMF,WAAWF,kBAAkBC;IACnC,MAAMO,wBAAwBP,YAAYQ,OAAO,CAAC,QAAQ;IAE1D,IAAIC,MAAM,AAAGR,WAAS,QAAKI,WAAS,MAAGC,OACrCC,CAAAA,wBAAwB,AAAC,MAAGA,wBAA0B,EAAC;IAGzD,IAAIA,sBAAsBG,UAAU,CAAC,SAAS;QAC5CD,MAAM,AAAGR,WAAS,QAAKM,sBAAsBI,KAAK,CAAC,MAAM,CAAC,EAAE;IAC9D;IAEA,OAAOF;AACT"}