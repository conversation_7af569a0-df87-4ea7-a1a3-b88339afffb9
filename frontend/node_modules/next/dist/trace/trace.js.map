{"version": 3, "sources": ["../../src/trace/trace.ts"], "names": ["Span", "trace", "flushAllTraces", "NUM_OF_MICROSEC_IN_NANOSEC", "BigInt", "count", "getId", "SpanStatus", "Started", "Stopped", "constructor", "name", "parentId", "attrs", "startTime", "duration", "status", "id", "_start", "process", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "reporter", "report", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "span", "setAttribute", "key", "value", "String", "traceFn", "fn", "traceAsyncFn", "flushAll"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAiBaA,IAAI;eAAJA;;IAmGAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;;wBA3HY;AAEzB,MAAMC,6BAA6BC,OAAO;AAC1C,IAAIC,QAAQ;AACZ,MAAMC,QAAQ;IACZD;IACA,OAAOA;AACT;IAIO;UAAKE,UAAU;IAAVA,WAAAA,WACVC,aAAAA,KAAAA;IADUD,WAAAA,WAEVE,aAAAA,KAAAA;GAFUF,eAAAA;AAKL,MAAMP;IAaXU,YAAY,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,SAAS,EAMV,CAAE;QACD,IAAI,CAACH,IAAI,GAAGA;QACZ,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACG,QAAQ,GAAG;QAChB,IAAI,CAACF,KAAK,GAAGA,QAAQ;YAAE,GAAGA,KAAK;QAAC,IAAI,CAAC;QACrC,IAAI,CAACG,MAAM,GAhCbR;QAiCE,IAAI,CAACS,EAAE,GAAGX;QACV,IAAI,CAACY,MAAM,GAAGJ,aAAaK,QAAQC,MAAM,CAACC,MAAM;QAChD,wEAAwE;QACxE,iDAAiD;QACjD,2IAA2I;QAC3I,wDAAwD;QACxD,iFAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,KAAKD,GAAG;IACrB;IAEA,yEAAyE;IACzE,+DAA+D;IAC/D,wEAAwE;IACxE,yCAAyC;IACzCE,KAAKC,QAAiB,EAAE;QACtB,MAAMC,MAAcD,YAAYN,QAAQC,MAAM,CAACC,MAAM;QACrD,MAAMN,WAAW,AAACW,CAAAA,MAAM,IAAI,CAACR,MAAM,AAAD,IAAKf;QACvC,IAAI,CAACa,MAAM,GAjDbP;QAkDE,IAAIM,WAAWY,OAAOC,gBAAgB,EAAE;YACtC,MAAM,IAAIC,MAAM,CAAC,4CAA4C,EAAEd,SAAS,CAAC;QAC3E;QACA,MAAMe,YAAY,IAAI,CAACZ,MAAM,GAAGf;QAChC4B,gBAAQ,CAACC,MAAM,CACb,IAAI,CAACrB,IAAI,EACTgB,OAAOZ,WACPY,OAAOG,YACP,IAAI,CAACb,EAAE,EACP,IAAI,CAACL,QAAQ,EACb,IAAI,CAACC,KAAK,EACV,IAAI,CAACS,GAAG;IAEZ;IAEAW,WAAWtB,IAAY,EAAEE,KAAc,EAAE;QACvC,OAAO,IAAIb,KAAK;YAAEW;YAAMC,UAAU,IAAI,CAACK,EAAE;YAAEJ;QAAM;IACnD;IAEAqB,iBACEvB,IAAY,EACZ,yCAAyC;IACzCG,SAAiB,EACjB,wCAAwC;IACxCW,QAAgB,EAChBZ,KAAc,EACd;QACA,MAAMsB,OAAO,IAAInC,KAAK;YAAEW;YAAMC,UAAU,IAAI,CAACK,EAAE;YAAEJ;YAAOC;QAAU;QAClEqB,KAAKX,IAAI,CAACC;IACZ;IAEAW,aAAaC,GAAW,EAAEC,KAAU,EAAE;QACpC,IAAI,CAACzB,KAAK,CAACwB,IAAI,GAAGE,OAAOD;IAC3B;IAEAE,QAAWC,EAAqB,EAAK;QACnC,IAAI;YACF,OAAOA,GAAG,IAAI;QAChB,SAAU;YACR,IAAI,CAACjB,IAAI;QACX;IACF;IAEA,MAAMkB,aAAgBD,EAAkC,EAAc;QACpE,IAAI;YACF,OAAO,MAAMA,GAAG,IAAI;QACtB,SAAU;YACR,IAAI,CAACjB,IAAI;QACX;IACF;AACF;AAEO,MAAMvB,QAAQ,CACnBU,MACAC,UACAC;IAEA,OAAO,IAAIb,KAAK;QAAEW;QAAMC;QAAUC;IAAM;AAC1C;AAEO,MAAMX,iBAAiB,IAAM6B,gBAAQ,CAACY,QAAQ"}