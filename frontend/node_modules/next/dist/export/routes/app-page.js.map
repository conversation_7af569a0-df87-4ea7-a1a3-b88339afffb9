{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["generatePrefetchRsc", "exportAppPage", "ExportedAppPageFiles", "HTML", "FLIGHT", "META", "POSTPONED", "req", "path", "res", "pathname", "htmlFilepath", "renderOpts", "fileWriter", "headers", "RSC", "toLowerCase", "NEXT_URL", "NEXT_ROUTER_PREFETCH", "supportsDynamicHTML", "isPrefetch", "isRevalidate", "prefetchRenderResult", "lazyRenderAppPage", "pipe", "hasStreamed", "prefetchRscData", "<PERSON><PERSON><PERSON>", "concat", "buffers", "store", "staticPrefetchBailout", "replace", "page", "query", "debugOutput", "isDynamicError", "isAppPrefetch", "revalidate", "result", "html", "toUnchunkedString", "metadata", "flightData", "pageData", "Error", "staticBailoutInfo", "description", "err", "stack", "message", "substring", "indexOf", "console", "warn", "fetchTags", "NEXT_CACHE_TAGS_HEADER", "meta", "JSON", "stringify", "hasNextSupport", "undefined", "isDynamicUsageError"], "mappings": ";;;;;;;;;;;;;;;;;;;IA0BsBA,mBAAmB;eAAnBA;;IAuCAC,aAAa;eAAbA;;;kCApDf;qCAC6B;2BACG;wBACR;8BACG;IAE3B;UAAWC,oBAAoB;IAApBA,qBAChBC,UAAAA;IADgBD,qBAEhBE,YAAAA;IAFgBF,qBAGhBG,UAAAA;IAHgBH,qBAIhBI,eAAAA;GAJgBJ,yBAAAA;AAOX,eAAeF,oBACpBO,GAAkB,EAClBC,IAAY,EACZC,GAAmB,EACnBC,QAAgB,EAChBC,YAAoB,EACpBC,UAAsB,EACtBC,UAAsB;IAEtBN,IAAIO,OAAO,CAACC,qBAAG,CAACC,WAAW,GAAG,GAAG;IACjCT,IAAIO,OAAO,CAACG,0BAAQ,CAACD,WAAW,GAAG,GAAGR;IACtCD,IAAIO,OAAO,CAACI,sCAAoB,CAACF,WAAW,GAAG,GAAG;IAElDJ,WAAWO,mBAAmB,GAAG;IACjCP,WAAWQ,UAAU,GAAG;IACxB,OAAOR,WAAWS,YAAY;IAE9B,MAAMC,uBAAuB,MAAMC,IAAAA,+BAAiB,EAClDhB,KACAE,KACAC,UACA,CAAC,GACDE;IAGFU,qBAAqBE,IAAI,CAACf;IAC1B,MAAMA,IAAIgB,WAAW;IAErB,MAAMC,kBAAkBC,OAAOC,MAAM,CAACnB,IAAIoB,OAAO;IAEjD,IAAI,AAACjB,WAAmBkB,KAAK,CAACC,qBAAqB,EAAE;IAErD,MAAMlB,WArCG,UAuCPF,aAAaqB,OAAO,CAAC,WAAW,kBAChCN;AAEJ;AAEO,eAAezB,cACpBM,GAAkB,EAClBE,GAAmB,EACnBwB,IAAY,EACZzB,IAAY,EACZE,QAAgB,EAChBwB,KAAyB,EACzBtB,UAAsB,EACtBD,YAAoB,EACpBwB,WAAoB,EACpBC,cAAuB,EACvBC,aAAsB,EACtBxB,UAAsB;IAEtB,6EAA6E;IAC7E,IAAIoB,SAAS,eAAe;QAC1BvB,WAAW;IACb;IAEA,IAAI;QACF,IAAI2B,eAAe;YACjB,MAAMrC,oBACJO,KACAC,MACAC,KACAC,UACAC,cACAC,YACAC;YAGF,OAAO;gBAAEyB,YAAY;YAAE;QACzB;QAEA,MAAMC,SAAS,MAAMhB,IAAAA,+BAAiB,EACpChB,KACAE,KACAC,UACAwB,OACAtB;QAEF,MAAM4B,OAAOD,OAAOE,iBAAiB;QACrC,MAAM,EAAEC,QAAQ,EAAE,GAAGH;QACrB,MAAMI,aAAaD,SAASE,QAAQ;QACpC,MAAMN,aAAaI,SAASJ,UAAU,IAAI;QAE1C,IAAIA,eAAe,GAAG;YACpB,IAAIF,gBAAgB;gBAClB,MAAM,IAAIS,MACR,CAAC,+DAA+D,EAAErC,KAAK,CAAC,CAAC;YAE7E;YAEA,IAAI,CAAC,AAACI,WAAmBkB,KAAK,CAACC,qBAAqB,EAAE;gBACpD,MAAM/B,oBACJO,KACAC,MACAC,KACAC,UACAC,cACAC,YACAC;YAEJ;YAEA,MAAM,EAAEiC,oBAAoB,CAAC,CAAC,EAAE,GAAGJ;YAEnC,IAAIJ,eAAe,KAAKH,gBAAeW,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrE,MAAMC,MAAM,IAAIH,MACd,CAAC,iDAAiD,EAAErC,KAAK,UAAU,EAAEsC,kBAAkBC,WAAW,CAAC,CAAC;gBAGtG,4DAA4D;gBAC5D,MAAM,EAAEE,KAAK,EAAE,GAAGH;gBAClB,IAAIG,OAAO;oBACTD,IAAIC,KAAK,GAAGD,IAAIE,OAAO,GAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;gBAC1D;gBAEAC,QAAQC,IAAI,CAACN;YACf;YAEA,OAAO;gBAAEV,YAAY;YAAE;QACzB;QAEA,IAAIxB;QACJ,IAAI4B,SAASa,SAAS,EAAE;YACtBzC,UAAU;gBAAE,CAAC0C,iCAAsB,CAAC,EAAEd,SAASa,SAAS;YAAC;QAC3D;QAEA,iCAAiC;QACjC,MAAM1C,WAvID,QAyIHF,cACA6B,QAAQ,IACR;QAGF,0CAA0C;QAC1C,MAAMiB,OAAO;YAAE3C;QAAQ;QACvB,MAAMD,WA9ID,QAgJHF,aAAaqB,OAAO,CAAC,WAAW,UAChC0B,KAAKC,SAAS,CAACF;QAGjB,qCAAqC;QACrC,MAAM5C,WAtJC,UAwJLF,aAAaqB,OAAO,CAAC,WAAW,SAChCW;QAGF,OAAO;YACL,iEAAiE;YACjED,UAAUkB,sBAAc,GAAGH,OAAOI;YAClCvB;QACF;IACF,EAAE,OAAOU,KAAU;QACjB,IAAI,CAACc,IAAAA,wCAAmB,EAACd,MAAM;YAC7B,MAAMA;QACR;QAEA,OAAO;YAAEV,YAAY;QAAE;IACzB;AACF"}