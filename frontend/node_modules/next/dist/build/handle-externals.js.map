{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["isResourceInPackages", "resolveExternal", "makeExternalHandler", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "hasAppDir", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackAppLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "resolveNextExternal", "isExternal", "isWebpackServerLayer", "WEBPACK_LAYERS", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "defaultOverrides", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "shouldBeBundled", "bundlePagesExternals"], "mappings": ";;;;;;;;;;;;;;;;IAwBgBA,oBAAoB;eAApBA;;IAgBMC,eAAe;eAAfA;;IA6FNC,mBAAmB;eAAnBA;;;2BArIe;6BAEE;4BACU;6DAC1B;+BAMV;wBACiD;;;;;;AAExD,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAG9C,SAASN,qBACdU,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,OAAOD,gCAAAA,aAAcE,IAAI,CAAC,CAACC,IACzBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMI,aAAI,CAACC,GAAG,IACxDT,SAASU,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBP,EAAEQ,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAelB,gBACpBsB,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,SAAkB,EAClBC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAAChB;IACvB,MAAMiB,oBAAoBjB,uBAAuB;IAEjD,IAAIkB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,IAAIC,mBACFJ,gBAAgBb,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAC1D,kFAAkF;IAClF,sCAAsC;IACtC,IAAIC,WAAW;QACbgB,mBAAmB;YAAC;SAAM;IAC5B;IACA,KAAK,MAAMC,aAAaD,iBAAkB;QACxC,MAAME,UAAUjB,WACdgB,YAAYb,oBAAoBE;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACQ,KAAKC,MAAM,GAAG,MAAMG,QAAQrB,SAASC;QACzC,EAAE,OAAOqB,KAAK;YACZL,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEkB,UAAUlB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAIkB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAActB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAY5B,KAAKG;YACjD,EAAE,OAAOqB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYP,OAAOC,UAAUO,WAAW;gBAC1CR,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAASzC,oBAAoB,EAClCkD,MAAM,EACNC,0BAA0B,EAC1B9B,GAAG,EACHK,SAAS,EAMV;QAE2BwB;IAD1B,IAAIE;IACJ,MAAMb,oBAAoBW,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBZ,YAAY,MAAK;IAEhE,OAAO,eAAegB,gBACpB/B,OAAe,EACfC,OAAe,EACf+B,cAAsB,EACtBC,KAA8B,EAC9B7B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM8B,UACJjC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBE,aAAI,CAAC0C,KAAK,CAACC,UAAU,CAACnC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBoC,QAAQC,QAAQ,KAAK,WAAW7C,aAAI,CAAC8C,KAAK,CAACH,UAAU,CAACnC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMuC,aAAaC,IAAAA,yBAAiB,EAACR;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaQ,IAAI,CAACzC,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIvB,mBAAmBgE,IAAI,CAACzC,YAAY,CAACuC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEvC,QAAQ,CAAC;YAC9B;YAEA,MAAM0C,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACzC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQN,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIM,QAAQV,UAAU,CAACqD,sCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAM1C,iBAAiB8B,mBAAmB;QAE1C;;;;;;KAMC,GACD,MAAMa,sBAAsB,CAACtB;YAC3B,MAAMuB,aAAa/D,gBAAgB2D,IAAI,CAACnB;YAExC,sFAAsF;YACtF,sGAAsG;YACtG,IAAIuB,YAAY;gBACd,oGAAoG;gBACpG,oCAAoC;gBACpC,OAAO,CAAC,SAAS,EAAEvB,SAAS1B,OAAO,CAAC,oBAAoB,aAAa,CAAC;YACxE;QACF;QAEA,4DAA4D;QAC5D,yFAAyF;QACzF,IACEkD,IAAAA,4BAAoB,EAACd,UACrBhC,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDmD,IAAI,CAACzC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CyC,IAAI,CAACzC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DyC,IAAI,CAChEzC,YAEF,4CAA4CyC,IAAI,CAACzC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEyC,IAAI,CACxEzC,YAEF,2CAA2CyC,IAAI,CAACzC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO4C,oBAAoB5C;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,IAAIgC,UAAUe,yBAAc,CAACC,mBAAmB,EAAE;YAChD,MAAMC,aAAajD,QAAQV,UAAU,CAAC;YACtC,MAAM4D,cAAcD,aAChBzD,aAAI,CAACG,IAAI,CAACI,SAASC,SAASJ,OAAO,CAAC,OAAO,OAC3CI;YACJ,OAAO4C,oBAAoBM;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAM5E,gBAC1BsB,KACA6B,OAAOG,YAAY,CAACf,YAAY,EAChCf,SACAC,SACAC,gBACAC,WACAC,YACA8B,UAAUW,sBAAsBQ;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc7B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAItB,YAAY,oBAAoB;YAClCmD,cAAcnC,GAAG,GAAGqC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAErC,GAAG,EAAEC,KAAK,EAAE,GAAGkC;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAACnC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD,MAAM,IAAIuC,MACR,CAAC,cAAc,EAAEtD,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMuD,eAAetC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CwB,IAAI,CAACzB,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2ByB,IAAI,CAACzB,QAChC,8BAA8ByB,IAAI,CAACzB,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIU,OAAO8B,iBAAiB,IAAI,CAAC5B,6BAA6B;YAC5DA,8BAA8B,IAAI6B;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAOhC,OAAO8B,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMpF,gBACnBsB,KACA6B,OAAOG,YAAY,CAACf,YAAY,EAChCf,SACA2D,MAAM,iBACNxD,WACAD,gBACAE,YACA8B,UAAUW,sBAAsBQ;gBAElC,IAAIO,OAAO3C,GAAG,EAAE;oBACdY,4BAA4BgC,GAAG,CAACF,KAAKlE,aAAI,CAACqE,OAAO,CAACF,OAAO3C,GAAG;gBAC9D;YACF;QACF;QAEA,sFAAsF;QACtF,gFAAgF;QAChF,wEAAwE;QACxE,MAAM8C,kBACJxF,qBACE0C,KACAU,OAAO8B,iBAAiB,EACxB5B,gCAEDX,SAASsB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAACkC,oBAAoB;QAE1D,IAAI,gCAAgCtB,IAAI,CAACzB,MAAM;YAC7C,IAAI8B,IAAAA,4BAAoB,EAACd,QAAQ;gBAC/B,gFAAgF;gBAChF,gEAAgE;gBAEhE,IAAIL,2BAA2Bc,IAAI,CAACzB,MAAM;oBACxC,OAAO,CAAC,EAAEuC,aAAa,CAAC,EAAEvD,QAAQ,CAAC;gBACrC;gBAEA;YACF;YAEA,IAAI8D,iBAAiB;YAErB,kEAAkE;YAClE,uBAAuB;YACvB,OAAO,CAAC,EAAEP,aAAa,CAAC,EAAEvD,QAAQ,CAAC;QACrC;QAEA,IAAI8D,iBAAiB;IAErB,qCAAqC;IACvC;AACF"}