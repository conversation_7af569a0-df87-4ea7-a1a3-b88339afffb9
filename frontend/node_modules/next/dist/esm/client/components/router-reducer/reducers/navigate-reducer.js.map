{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["CacheStates", "fetchServerResponse", "createRecordFromThenable", "readRecordValue", "createHrefFromUrl", "invalidateCacheBelowFlightSegmentPath", "fillCacheWithDataProperty", "createOptimisticTree", "applyRouterStatePatchToTree", "shouldHardNavigate", "isNavigatingToNewRootLayout", "PrefetchKind", "handleMutable", "applyFlightData", "PrefetchCacheEntryStatus", "getPrefetchEntryCacheStatus", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchQueue", "handleExternalUrl", "state", "mutable", "url", "pendingPush", "previousTree", "tree", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "addRefetchToLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "data", "appliedPatch", "status", "READY", "subTreeData", "Map", "segmentPathsToFill", "map", "segmentPaths", "res", "bailOptimistic", "navigateReducer", "action", "isExternalUrl", "navigateType", "cache", "forceOptimisticNavigation", "shouldScroll", "pathname", "hash", "href", "prefetchCache", "isForCurrentTree", "JSON", "stringify", "toString", "prefetchValues", "get", "kind", "TEMPORARY", "split", "optimisticTree", "temporaryCacheNode", "fetchResponse", "nextUrl", "buildId", "optimisticFlightSegmentPath", "slice", "flat", "patchedTree", "hashFragment", "set", "Promise", "resolve", "prefetchTime", "Date", "now", "treeAtTimeOfPrefetch", "lastUsedTime", "process", "env", "NODE_ENV", "AUTO", "newPrefetchValue", "prefetchEntryCacheStatus", "bump", "flightData", "canonicalUrlOverride", "currentTree", "flightDataPath", "flightSegmentPathWithLeadingEmpty", "newTree", "applied", "reusable", "stale", "hardNavigate", "subSegment", "scrollableSegmentPath"], "mappings": "AAAA,SAASA,WAAW,QAAQ,2DAA0D;AAMtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,qCAAqC,QAAQ,+CAA8C;AACpG,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,oBAAoB,QAAQ,4BAA2B;AAChE,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,2BAA2B,QAAQ,sCAAqC;AAQjF,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SACEC,wBAAwB,EACxBC,2BAA2B,QACtB,qCAAoC;AAC3C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,aAAa,QAAQ,qBAAoB;AAElD,OAAO,SAASC,kBACdC,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;IACjCJ,QAAQK,aAAa,GAAG;IACxBL,QAAQM,YAAY,GAAGL;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQO,kBAAkB,GAAGC;IAE7B,OAAOhB,cAAcO,OAAOC;AAC9B;AAEA,SAASS,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,yBACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAqD;IAErD,IAAIC,eAAe;IAEnBL,SAASM,MAAM,GAAGjD,YAAYkD,KAAK;IACnCP,SAASQ,WAAW,GAAGP,aAAaO,WAAW;IAC/CR,SAASV,cAAc,GAAG,IAAImB,IAAIR,aAAaX,cAAc;IAE7D,MAAMoB,qBAAqBxB,0BAA0BiB,WAAWQ,GAAG,CACjE,CAACtB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMuB,gBAAgBF,mBAAoB;QAC7C,MAAMG,MAAMlD,0BACVqC,UACAC,cACAW,cACAR;QAEF,IAAI,EAACS,uBAAAA,IAAKC,cAAc,GAAE;YACxBT,eAAe;QACjB;IACF;IAEA,OAAOA;AACT;AACA,OAAO,SAASU,gBACdvC,KAA2B,EAC3BwC,MAAsB;IAEtB,MAAM,EACJtC,GAAG,EACHuC,aAAa,EACbC,YAAY,EACZC,KAAK,EACL1C,OAAO,EACP2C,yBAAyB,EACzBC,YAAY,EACb,GAAGL;IACJ,MAAM,EAAEM,QAAQ,EAAEC,IAAI,EAAE,GAAG7C;IAC3B,MAAM8C,OAAO/D,kBAAkBiB;IAC/B,MAAMC,cAAcuC,iBAAiB;IACrC,wFAAwF;IACxF7C,mBAAmBG,MAAMiD,aAAa;IAEtC,MAAMC,mBACJC,KAAKC,SAAS,CAACnD,QAAQG,YAAY,MAAM+C,KAAKC,SAAS,CAACpD,MAAMK,IAAI;IAEpE,IAAI6C,kBAAkB;QACpB,OAAOzD,cAAcO,OAAOC;IAC9B;IAEA,IAAIwC,eAAe;QACjB,OAAO1C,kBAAkBC,OAAOC,SAASC,IAAImD,QAAQ,IAAIlD;IAC3D;IAEA,IAAImD,iBAAiBtD,MAAMiD,aAAa,CAACM,GAAG,CAACtE,kBAAkBiB,KAAK;IAEpE,IACE0C,6BACAU,CAAAA,kCAAAA,eAAgBE,IAAI,MAAKhE,aAAaiE,SAAS,EAC/C;QACA,MAAM7C,WAAWkC,SAASY,KAAK,CAAC;QAChC,wDAAwD;QACxD9C,SAASU,IAAI,CAAC;QAEd,wBAAwB;QACxB,kGAAkG;QAClG,MAAMqC,iBAAiBvE,qBAAqBwB,UAAUZ,MAAMK,IAAI,EAAE;QAElE,8DAA8D;QAC9D,MAAMuD,qBAAgC;YACpC,GAAGjB,KAAK;QACV;QAEA,mDAAmD;QACnD,+DAA+D;QAC/DiB,mBAAmB9B,MAAM,GAAGjD,YAAYkD,KAAK;QAC7C6B,mBAAmB5B,WAAW,GAAGhC,MAAM2C,KAAK,CAACX,WAAW;QACxD4B,mBAAmB9C,cAAc,GAAG,IAAImB,IAAIjC,MAAM2C,KAAK,CAAC7B,cAAc;QAEtE,IAAIc,OAAyD;QAE7D,MAAMiC,gBAAgB;YACpB,IAAI,CAACjC,MAAM;gBACTA,OAAO7C,yBACLD,oBAAoBoB,KAAKyD,gBAAgB3D,MAAM8D,OAAO,EAAE9D,MAAM+D,OAAO;YAEzE;YACA,OAAOnC;QACT;QAEA,0EAA0E;QAC1E,6DAA6D;QAC7D,MAAMoC,8BAA8BpD,SACjCqD,KAAK,CAAC,GACN9B,GAAG,CAAC,CAACtB,UAAY;gBAAC;gBAAYA;aAAQ,EACtCqD,IAAI;QAEP,wGAAwG;QACxG,0HAA0H;QAC1H,MAAM7B,MAAMlD,0BACVyE,oBACA5D,MAAM2C,KAAK,EACXqB,6BACAH,eACA;QAGF,gFAAgF;QAChF,IAAI,EAACxB,uBAAAA,IAAKC,cAAc,GAAE;YACxBrC,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;YACjCJ,QAAQkE,WAAW,GAAGR;YACtB1D,QAAQE,WAAW,GAAGA;YACtBF,QAAQmE,YAAY,GAAGrB;YACvB9C,QAAQ4C,YAAY,GAAGA;YACvB5C,QAAQO,kBAAkB,GAAG,EAAE;YAC/BP,QAAQ0C,KAAK,GAAGiB;YAChB3D,QAAQM,YAAY,GAAGyC;YAEvBhD,MAAMiD,aAAa,CAACoB,GAAG,CAACpF,kBAAkBiB,KAAK,QAAQ;gBACrD0B,MAAMA,OAAO7C,yBAAyBuF,QAAQC,OAAO,CAAC3C,SAAS;gBAC/D,iEAAiE;gBACjE4B,MAAMhE,aAAaiE,SAAS;gBAC5Be,cAAcC,KAAKC,GAAG;gBACtBC,sBAAsB3E,MAAMK,IAAI;gBAChCuE,cAAcH,KAAKC,GAAG;YACxB;YAEA,OAAOjF,cAAcO,OAAOC;QAC9B;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAACqD,gBAAgB;QACnB,MAAM1B,OAAO7C,yBACXD,oBACEoB,KACAF,MAAMK,IAAI,EACVL,MAAM8D,OAAO,EACb9D,MAAM+D,OAAO,EACb,8EAA8E;QAC9E,0DAA0D;QAC1Dc,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBvF,aAAawF,IAAI,GAAGvE;QAIjE,MAAMwE,mBAAmB;YACvBrD,MAAM7C,yBAAyBuF,QAAQC,OAAO,CAAC3C;YAC/C,iEAAiE;YACjE4B,MACEqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBvF,aAAawF,IAAI,GACjBxF,aAAaiE,SAAS;YAC5Be,cAAcC,KAAKC,GAAG;YACtBC,sBAAsB3E,MAAMK,IAAI;YAChCuE,cAAc;QAChB;QAEA5E,MAAMiD,aAAa,CAACoB,GAAG,CAACpF,kBAAkBiB,KAAK,QAAQ+E;QACvD3B,iBAAiB2B;IACnB;IAEA,MAAMC,2BAA2BtF,4BAA4B0D;IAE7D,0DAA0D;IAC1D,MAAM,EAAEqB,oBAAoB,EAAE/C,IAAI,EAAE,GAAG0B;IAEvCxD,cAAcqF,IAAI,CAACvD;IAEnB,0FAA0F;IAC1F,MAAM,CAACwD,YAAYC,qBAAqB,GAAGrG,gBAAgB4C;IAE3D,iCAAiC;IACjC,IAAI,CAAC0B,eAAesB,YAAY,EAAE;QAChC,gGAAgG;QAChGtB,eAAesB,YAAY,GAAGH,KAAKC,GAAG;IACxC;IAEA,4DAA4D;IAC5D,IAAI,OAAOU,eAAe,UAAU;QAClC,OAAOrF,kBAAkBC,OAAOC,SAASmF,YAAYjF;IACvD;IAEA,IAAImF,cAActF,MAAMK,IAAI;IAC5B,IAAIoB,eAAezB,MAAM2C,KAAK;IAC9B,IAAInC,qBAA0C,EAAE;IAChD,KAAK,MAAM+E,kBAAkBH,WAAY;QACvC,MAAM1D,oBAAoB6D,eAAetB,KAAK,CAC5C,GACA,CAAC;QAEH,0DAA0D;QAC1D,MAAMtC,YAAY4D,eAAetB,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;QAE7C,sBAAsB;QACtB,MAAMuB,oCAAoC;YAAC;eAAO9D;SAAkB;QAEpE,wEAAwE;QACxE,IAAI+D,UAAUpG,4BACZ,sBAAsB;QACtBmG,mCACAF,aACA3D;QAGF,kGAAkG;QAClG,6IAA6I;QAC7I,IAAI8D,YAAY,MAAM;YACpBA,UAAUpG,4BACR,sBAAsB;YACtBmG,mCACAb,sBACAhD;QAEJ;QAEA,IAAI8D,YAAY,MAAM;YACpB,IAAIlG,4BAA4B+F,aAAaG,UAAU;gBACrD,OAAO1F,kBAAkBC,OAAOC,SAAS+C,MAAM7C;YACjD;YAEA,IAAIuF,UAAUhG,gBACZ+B,cACAkB,OACA4C,gBACAjC,eAAeE,IAAI,KAAK,UACtB0B,6BAA6BvF,yBAAyBgG,QAAQ;YAGlE,IACE,CAACD,WACDR,6BAA6BvF,yBAAyBiG,KAAK,EAC3D;gBACAF,UAAUnE,yBACRoB,OACAlB,cACAC,mBACAC,WACA,wCAAwC;gBACxC,IACE5C,yBACED,oBACEoB,KACAoF,aACAtF,MAAM8D,OAAO,EACb9D,MAAM+D,OAAO;YAIvB;YAEA,MAAM8B,eAAevG,mBACnB,sBAAsB;YACtBkG,mCACAF;YAGF,IAAIO,cAAc;gBAChBlD,MAAMb,MAAM,GAAGjD,YAAYkD,KAAK;gBAChC,mDAAmD;gBACnDY,MAAMX,WAAW,GAAGP,aAAaO,WAAW;gBAE5C9C,sCACEyD,OACAlB,cACAC;gBAEF,8EAA8E;gBAC9EzB,QAAQ0C,KAAK,GAAGA;YAClB,OAAO,IAAI+C,SAAS;gBAClBzF,QAAQ0C,KAAK,GAAGA;YAClB;YAEAlB,eAAekB;YACf2C,cAAcG;YAEd,KAAK,MAAMK,cAAcpF,0BAA0BiB,WAAY;gBAC7D,MAAMoE,wBAAwB;uBAAIrE;uBAAsBoE;iBAAW;gBACnE,kFAAkF;gBAClF,IACEC,qBAAqB,CAACA,sBAAsB9E,MAAM,GAAG,EAAE,KACvD,eACA;oBACAT,mBAAmBc,IAAI,CAACyE;gBAC1B;YACF;QACF;IACF;IAEA9F,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;IACjCJ,QAAQkE,WAAW,GAAGmB;IACtBrF,QAAQM,YAAY,GAAG8E,uBACnBpG,kBAAkBoG,wBAClBrC;IACJ/C,QAAQE,WAAW,GAAGA;IACtBF,QAAQO,kBAAkB,GAAGA;IAC7BP,QAAQmE,YAAY,GAAGrB;IACvB9C,QAAQ4C,YAAY,GAAGA;IAEvB,OAAOpD,cAAcO,OAAOC;AAC9B"}