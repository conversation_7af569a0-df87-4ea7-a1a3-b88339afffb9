{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["CacheStates", "createHrefFromUrl", "fillLazyItemsTillLeafWithHead", "extractPathFromFlightRouterState", "createInitialRouterState", "buildId", "initialTree", "children", "initialCanonicalUrl", "initialParallelRoutes", "isServer", "location", "initialHead", "cache", "status", "READY", "data", "subTreeData", "parallelRoutes", "Map", "size", "undefined", "tree", "prefetchCache", "pushRef", "pendingPush", "mpaNavigation", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "canonicalUrl", "nextUrl", "pathname"], "mappings": "AAIA,SAASA,WAAW,QAAQ,wDAAuD;AACnF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,gCAAgC,QAAQ,yBAAwB;AAazE,OAAO,SAASC,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACkB,GATU;IAUvC,MAAMC,QAAmB;QACvBC,QAAQd,YAAYe,KAAK;QACzBC,MAAM;QACNC,aAAaV;QACb,oJAAoJ;QACpJW,gBAAgBR,WAAW,IAAIS,QAAQV;IACzC;IAEA,yEAAyE;IACzE,IAAIA,0BAA0B,QAAQA,sBAAsBW,IAAI,KAAK,GAAG;QACtElB,8BAA8BW,OAAOQ,WAAWf,aAAaM;IAC/D;QAsBI,sEAAsE;IACrET;IArBL,OAAO;QACLE;QACAiB,MAAMhB;QACNO;QACAU,eAAe,IAAIJ;QACnBK,SAAS;YAAEC,aAAa;YAAOC,eAAe;QAAM;QACpDC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAC,cACE,6EAA6E;QAC7E,kJAAkJ;QAClJrB,WAEIV,kBAAkBU,YAClBH;QACNyB,SAEE,CAAC9B,OAAAA,iCAAiCG,iBAAgBK,4BAAAA,SAAUuB,QAAQ,aAAnE/B,OACD;IACJ;AACF"}