{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "join", "requirePage", "interopDefault", "getTracer", "LoadComponentsSpan", "loadManifest", "wait", "loadManifestWithRetries", "manifestPath", "attempts", "err", "loadClientReferenceManifest", "entryName", "process", "env", "NEXT_MINIMAL", "__non_webpack_require__", "require", "globalThis", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "ComponentMod", "hasClientManifest", "endsWith", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "replace", "catch", "Component", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "loadComponents", "wrap"], "mappings": "AAeA,SACEA,cAAc,EACdC,uBAAuB,EACvBC,yBAAyB,EACzBC,yBAAyB,QACpB,0BAAyB;AAChC,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,WAAW,QAAQ,YAAW;AACvC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,IAAI,QAAQ,cAAa;AA2BlC;;CAEC,GACD,OAAO,eAAeC,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOJ,aAAaG;QACtB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMJ,KAAK;QACb;IACF;AACF;AAEA,eAAeK,4BACbH,YAAoB,EACpBI,SAAiB;IAEjBC,QAAQC,GAAG,CAACC,YAAY,GAEpBC,wBAAwBR,gBACxBS,QAAQT;IACZ,IAAI;QACF,OAAO,AAACU,WAAmBC,cAAc,CACvCP,UACD;IACH,EAAE,OAAOF,KAAK;QACZ,OAAOU;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EAKV;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACF,WAAW;QACb,CAACC,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM7B,YAAY,cAAcqB,SAAS;YAChEK,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM7B,YAAY,SAASqB,SAAS;SAC5D;IACH;IACA,MAAMS,eAAe,MAAMJ,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChD7B,YAAYsB,MAAMD,SAASE;IAG7B,6DAA6D;IAC7D,MAAMQ,oBACJR,aACCD,CAAAA,KAAKU,QAAQ,CAAC,YAAYV,SAAS,gBAAgBA,SAAS,aAAY;IAE3E,MAAM,CACJW,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMV,QAAQC,GAAG,CAAC;QACpBrB,wBAAuCP,KAAKsB,SAAS1B;QACrDW,wBACEP,KAAKsB,SAASzB;QAEhBmC,oBACIrB,4BACEX,KACEsB,SACA,UACA,OACAC,KAAKe,OAAO,CAAC,QAAQ,OAAO,MAAMxC,4BAA4B,QAEhEyB,KAAKe,OAAO,CAAC,QAAQ,QAEvBlB;QACJI,YACIjB,wBACEP,KAAKsB,SAAS,UAAUvB,4BAA4B,UACpDwC,KAAK,CAAC,IAAM,QACd;KACL;IAED,MAAMC,YAAYtC,eAAe6B;IACjC,MAAMU,WAAWvC,eAAeuB;IAChC,MAAMiB,MAAMxC,eAAewB;IAE3B,MAAM,EAAEiB,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvEf;IAEF,OAAO;QACLW;QACAD;QACAD;QACAN;QACAC;QACAY,YAAYhB,aAAaiB,MAAM,IAAI,CAAC;QACpCjB;QACAY;QACAC;QACAC;QACAT;QACAC;QACAb;QACAD;QACAuB;IACF;AACF;AAEA,OAAO,MAAMG,iBAAiB9C,YAAY+C,IAAI,CAC5C9C,mBAAmB6C,cAAc,EACjC5B,oBACD"}