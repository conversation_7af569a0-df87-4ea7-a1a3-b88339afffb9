{"version": 3, "sources": ["../../../../../src/server/future/route-modules/app-route/module.ts"], "names": ["RouteModule", "RequestAsyncStorageWrapper", "StaticGenerationAsyncStorageWrapper", "handleBadRequestResponse", "handleInternalServerErrorResponse", "HTTP_METHODS", "isHTTPMethod", "addImplicitTags", "patchFetch", "getTracer", "AppRouteRouteHandlersSpan", "getPathnameFromAbsolutePath", "proxyRequest", "resolveHandlerError", "Log", "autoImplementMethods", "getNonStaticMethods", "appendMutableCookies", "RouteKind", "parsedUrlQueryToParams", "serverHooks", "header<PERSON><PERSON>s", "staticGenerationBailout", "requestAsyncStorage", "staticGenerationAsyncStorage", "actionAsyncStorage", "sharedModules", "AppRouteRouteModule", "is", "route", "definition", "kind", "APP_ROUTE", "constructor", "userland", "resolvedPagePath", "nextConfigOutput", "methods", "nonStaticMethods", "dynamic", "Error", "pathname", "process", "env", "NODE_ENV", "lowercased", "map", "method", "toLowerCase", "error", "toUpperCase", "some", "resolve", "execute", "request", "context", "handler", "requestContext", "req", "renderOpts", "previewProps", "prerenderManifest", "preview", "staticGenerationContext", "urlPathname", "nextUrl", "fetchCache", "response", "run", "isAppRoute", "wrap", "staticGenerationStore", "join", "forceDynamic", "forceStatic", "dynamicShouldError", "revalidate", "wrappedRequest", "getRootSpanAttributes", "set", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "res", "params", "undefined", "Response", "fetchMetrics", "waitUntil", "Promise", "all", "pendingRevalidates", "fetchTags", "tags", "requestStore", "getStore", "mutableCookies", "headers", "Headers", "body", "status", "statusText", "has", "get", "handle", "err"], "mappings": "AAMA,SACEA,WAAW,QAGN,kBAAiB;AACxB,SACEC,0BAA0B,QAErB,uDAAsD;AAC7D,SACEC,mCAAmC,QAE9B,iEAAgE;AACvE,SACEC,wBAAwB,EACxBC,iCAAiC,QAC5B,+BAA8B;AACrC,SAA2BC,YAAY,EAAEC,YAAY,QAAQ,oBAAmB;AAChF,SAASC,eAAe,EAAEC,UAAU,QAAQ,2BAA0B;AACtE,SAASC,SAAS,QAAQ,4BAA2B;AACrD,SAASC,yBAAyB,QAAQ,+BAA8B;AACxE,SAASC,2BAA2B,QAAQ,4CAA2C;AACvF,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,YAAYC,SAAS,+BAA8B;AACnD,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,oBAAoB,QAAQ,uDAAsD;AAC3F,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,sBAAsB,QAAQ,uCAAsC;AAE7E,YAAYC,iBAAiB,qDAAoD;AACjF,YAAYC,iBAAiB,wCAAuC;AACpE,SAASC,uBAAuB,QAAQ,0DAAyD;AAEjG,SAASC,mBAAmB,QAAQ,+DAA8D;AAClG,SAASC,4BAA4B,QAAQ,yEAAwE;AACrH,SAASC,kBAAkB,QAAQ,8DAA6D;AAChG,YAAYC,mBAAmB,mBAAkB;AAsEjD;;CAEC,GACD,OAAO,MAAMC,4BAA4B3B;qBAgChB0B,gBAAgBA;IAevC,OAAcE,GAAGC,KAAkB,EAAgC;QACjE,OAAOA,MAAMC,UAAU,CAACC,IAAI,KAAKb,UAAUc,SAAS;IACtD;IAEAC,YAAY,EACVC,QAAQ,EACRJ,UAAU,EACVK,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEF;YAAUJ;QAAW;QArD/B;;GAEC,QACeP,sBAAsBA;QAEtC;;GAEC,QACeC,+BAA+BA;QAE/C;;;GAGC,QACeJ,cAAcA;QAE9B;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,0BAA0BA;QAI1C;;;GAGC,QACeG,qBAAqBA;QAqBnC,IAAI,CAACU,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACC,OAAO,GAAGtB,qBAAqBmB;QAEpC,6CAA6C;QAC7C,IAAI,CAACI,gBAAgB,GAAGtB,oBAAoBkB;QAE5C,qDAAqD;QACrD,IAAI,CAACK,OAAO,GAAG,IAAI,CAACL,QAAQ,CAACK,OAAO;QACpC,IAAI,IAAI,CAACH,gBAAgB,KAAK,UAAU;YACtC,IAAI,CAAC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,QAAQ;gBAC5C,IAAI,CAACA,OAAO,GAAG;YACjB,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,iBAAiB;gBAC3C,MAAM,IAAIC,MACR,CAAC,gDAAgD,EAAEV,WAAWW,QAAQ,CAAC,wHAAwH,CAAC;YAEpM;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAaxC,aAAayC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUF,WAAY;gBAC/B,IAAIE,UAAU,IAAI,CAACb,QAAQ,EAAE;oBAC3BpB,IAAImC,KAAK,CACP,CAAC,2BAA2B,EAAEF,OAAO,MAAM,EACzC,IAAI,CAACZ,gBAAgB,CACtB,yBAAyB,EAAEY,OAAOG,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAAChB,QAAQ,EAAE;gBAC9BpB,IAAImC,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACd,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAAC9B,aAAa8C,IAAI,CAAC,CAACJ,SAAWA,UAAU,IAAI,CAACb,QAAQ,GAAG;gBAC3DpB,IAAImC,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACd,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQiB,QAAQL,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACzC,aAAayC,SAAS,OAAO5C;QAElC,sBAAsB;QACtB,OAAO,IAAI,CAACkC,OAAO,CAACU,OAAO;IAC7B;IAEA;;GAEC,GACD,MAAcM,QACZC,OAAoB,EACpBC,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMC,UAAU,IAAI,CAACJ,OAAO,CAACE,QAAQP,MAAM;QAE3C,mCAAmC;QACnC,MAAMU,iBAAiC;YACrCC,KAAKJ;QACP;QAGEG,eAAuBE,UAAU,GAAG;YACpCC,cAAcL,QAAQM,iBAAiB,CAACC,OAAO;QACjD;QAEA,6CAA6C;QAC7C,MAAMC,0BAAmD;YACvDC,aAAaV,QAAQW,OAAO,CAACxB,QAAQ;YACrCkB,YAAYJ,QAAQI,UAAU;QAChC;QAEA,+CAA+C;QAC/CI,wBAAwBJ,UAAU,CAACO,UAAU,GAAG,IAAI,CAAChC,QAAQ,CAACgC,UAAU;QAExE,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMC,WAAoB,MAAM,IAAI,CAAC1C,kBAAkB,CAAC2C,GAAG,CACzD;YACEC,YAAY;QACd,GACA,IACEpE,2BAA2BqE,IAAI,CAC7B,IAAI,CAAC/C,mBAAmB,EACxBkC,gBACA,IACEvD,oCAAoCoE,IAAI,CACtC,IAAI,CAAC9C,4BAA4B,EACjCuC,yBACA,CAACQ;wBAuDC9D;oBAtDA,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAAC6B,gBAAgB,EAAE;wBACzB,IAAI,CAAChB,uBAAuB,CAC1B,CAAC,wBAAwB,EAAE,IAAI,CAACgB,gBAAgB,CAACkC,IAAI,CACnD,MACA,CAAC;oBAEP;oBAEA,oEAAoE;oBACpE,OAAQ,IAAI,CAACjC,OAAO;wBAClB,KAAK;4BACH,6DAA6D;4BAC7D,gCAAgC;4BAChCgC,sBAAsBE,YAAY,GAAG;4BACrC,IAAI,CAACnD,uBAAuB,CAAC,CAAC,aAAa,CAAC,EAAE;gCAC5CiB,SAAS,IAAI,CAACA,OAAO;4BACvB;4BACA;wBACF,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BgC,sBAAsBG,WAAW,GAAG;4BACpC;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDH,sBAAsBI,kBAAkB,GAAG;4BAC3C;wBACF;4BACE;oBACJ;oBAEA,kEAAkE;oBAClE,oEAAoE;oBACpE,8BAA8B;oBAC9BJ,sBAAsBK,UAAU,KAC9B,IAAI,CAAC1C,QAAQ,CAAC0C,UAAU,IAAI;oBAE9B,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAMC,iBAAiBjE,aACrB0C,SACA;wBAAEf,SAAS,IAAI,CAACA,OAAO;oBAAC,GACxB;wBACElB,aAAa,IAAI,CAACA,WAAW;wBAC7BD,aAAa,IAAI,CAACA,WAAW;wBAC7BE,yBAAyB,IAAI,CAACA,uBAAuB;oBACvD;oBAGF,mDAAmD;oBACnD,MAAMO,QAAQlB,4BAA4B,IAAI,CAACwB,gBAAgB;qBAC/D1B,mCAAAA,YAAYqE,qBAAqB,uBAAjCrE,iCAAqCsE,GAAG,CAAC,cAAclD;oBACvD,OAAOpB,YAAYuE,KAAK,CACtBtE,0BAA0BuE,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAErD,MAAM,CAAC;wBAC9CsD,YAAY;4BACV,cAActD;wBAChB;oBACF,GACA;4BA0BI0C;wBAzBF,0BAA0B;wBAC1B/D,WAAW;4BACTY,aAAa,IAAI,CAACA,WAAW;4BAC7BI,8BACE,IAAI,CAACA,4BAA4B;wBACrC;wBACA,MAAM4D,MAAM,MAAM5B,QAAQqB,gBAAgB;4BACxCQ,QAAQ9B,QAAQ8B,MAAM,GAClBlE,uBAAuBoC,QAAQ8B,MAAM,IACrCC;wBACN;wBACA,IAAI,CAAEF,CAAAA,eAAeG,QAAO,GAAI;4BAC9B,MAAM,IAAI/C,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACL,gBAAgB,CAAC,0FAA0F,CAAC;wBAEpK;wBACEoB,QAAQI,UAAU,CAAS6B,YAAY,GACvCjB,sBAAsBiB,YAAY;wBAEpCjC,QAAQI,UAAU,CAAC8B,SAAS,GAAGC,QAAQC,GAAG,CACxCpB,sBAAsBqB,kBAAkB,IAAI,EAAE;wBAGhDrF,gBAAgBgE;wBACdhB,QAAQI,UAAU,CAASkC,SAAS,IACpCtB,8BAAAA,sBAAsBuB,IAAI,qBAA1BvB,4BAA4BC,IAAI,CAAC;wBAEnC,4DAA4D;wBAC5D,0DAA0D;wBAC1D,QAAQ;wBACR,MAAMuB,eAAe,IAAI,CAACxE,mBAAmB,CAACyE,QAAQ;wBACtD,IAAID,gBAAgBA,aAAaE,cAAc,EAAE;4BAC/C,MAAMC,UAAU,IAAIC,QAAQf,IAAIc,OAAO;4BACvC,IACEjF,qBACEiF,SACAH,aAAaE,cAAc,GAE7B;gCACA,OAAO,IAAIV,SAASH,IAAIgB,IAAI,EAAE;oCAC5BC,QAAQjB,IAAIiB,MAAM;oCAClBC,YAAYlB,IAAIkB,UAAU;oCAC1BJ;gCACF;4BACF;wBACF;wBAEA,OAAOd;oBACT;gBAEJ;QAKV,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAEjB,CAAAA,oBAAoBoB,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAOnF;QACT;QAEA,IAAI+D,SAAS+B,OAAO,CAACK,GAAG,CAAC,yBAAyB;YAChD,oEAAoE;YACpE,6EAA6E;YAC7E,MAAM,IAAI/D,MACR;QAGF,6EAA6E;QAC7E,iEAAiE;QAEjE,2EAA2E;QAC3E,6EAA6E;QAC7E,0EAA0E;QAC1E,mCAAmC;QACnC,sBAAsB;QACtB,8CAA8C;QAC9C,IAAI;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,oEAAoE;QACpE,0DAA0D;QAC1D,qEAAqE;QACrE,yDAAyD;QAC3D;QAEA,IAAI2B,SAAS+B,OAAO,CAACM,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,IAAIhE,MACR;QAEJ;QAEA,OAAO2B;IACT;IAEA,MAAasC,OACXnD,OAAoB,EACpBC,OAAoC,EACjB;QACnB,IAAI;YACF,yCAAyC;YACzC,MAAMY,WAAW,MAAM,IAAI,CAACd,OAAO,CAACC,SAASC;YAE7C,uCAAuC;YACvC,OAAOY;QACT,EAAE,OAAOuC,KAAK;YACZ,+DAA+D;YAC/D,MAAMvC,WAAWtD,oBAAoB6F;YACrC,IAAI,CAACvC,UAAU,MAAMuC;YAErB,wCAAwC;YACxC,OAAOvC;QACT;IACF;AACF;AAEA,eAAexC,oBAAmB"}