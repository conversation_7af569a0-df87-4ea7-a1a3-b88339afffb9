{"version": 3, "sources": ["../../../src/shared/lib/base64-arraybuffer.ts"], "names": ["chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "encode", "arraybuffer", "bytes", "len", "base64", "substring", "decode", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,2FAA2F;AAE3F,MAAMA,QAAQ;AAEd,wCAAwC;AACxC,MAAMC,SAAS,OAAOC,eAAe,cAAc,EAAE,GAAG,IAAIA,WAAW;AACvE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,MAAMI,MAAM,EAAED,IAAK;IACrCF,MAAM,CAACD,MAAMK,UAAU,CAACF,GAAG,GAAGA;AAChC;AAEA,OAAO,MAAMG,SAAS,CAACC;IACrB,IAAIC,QAAQ,IAAIN,WAAWK,cACzBJ,GACAM,MAAMD,MAAMJ,MAAM,EAClBM,SAAS;IAEX,IAAKP,IAAI,GAAGA,IAAIM,KAAKN,KAAK,EAAG;QAC3BO,UAAUV,KAAK,CAACQ,KAAK,CAACL,EAAE,IAAI,EAAE;QAC9BO,UAAUV,KAAK,CAAC,AAAEQ,CAAAA,KAAK,CAACL,EAAE,GAAG,CAAA,KAAM,IAAMK,KAAK,CAACL,IAAI,EAAE,IAAI,EAAG;QAC5DO,UAAUV,KAAK,CAAC,AAAEQ,CAAAA,KAAK,CAACL,IAAI,EAAE,GAAG,EAAC,KAAM,IAAMK,KAAK,CAACL,IAAI,EAAE,IAAI,EAAG;QACjEO,UAAUV,KAAK,CAACQ,KAAK,CAACL,IAAI,EAAE,GAAG,GAAG;IACpC;IAEA,IAAIM,MAAM,MAAM,GAAG;QACjBC,SAASA,OAAOC,SAAS,CAAC,GAAGD,OAAON,MAAM,GAAG,KAAK;IACpD,OAAO,IAAIK,MAAM,MAAM,GAAG;QACxBC,SAASA,OAAOC,SAAS,CAAC,GAAGD,OAAON,MAAM,GAAG,KAAK;IACpD;IAEA,OAAOM;AACT,EAAC;AAED,OAAO,MAAME,SAAS,CAACF;IACrB,IAAIG,eAAeH,OAAON,MAAM,GAAG,MACjCK,MAAMC,OAAON,MAAM,EACnBD,GACAW,IAAI,GACJC,UACAC,UACAC,UACAC;IAEF,IAAIR,MAAM,CAACA,OAAON,MAAM,GAAG,EAAE,KAAK,KAAK;QACrCS;QACA,IAAIH,MAAM,CAACA,OAAON,MAAM,GAAG,EAAE,KAAK,KAAK;YACrCS;QACF;IACF;IAEA,MAAMN,cAAc,IAAIY,YAAYN,eAClCL,QAAQ,IAAIN,WAAWK;IAEzB,IAAKJ,IAAI,GAAGA,IAAIM,KAAKN,KAAK,EAAG;QAC3BY,WAAWd,MAAM,CAACS,OAAOL,UAAU,CAACF,GAAG;QACvCa,WAAWf,MAAM,CAACS,OAAOL,UAAU,CAACF,IAAI,GAAG;QAC3Cc,WAAWhB,MAAM,CAACS,OAAOL,UAAU,CAACF,IAAI,GAAG;QAC3Ce,WAAWjB,MAAM,CAACS,OAAOL,UAAU,CAACF,IAAI,GAAG;QAE3CK,KAAK,CAACM,IAAI,GAAG,AAACC,YAAY,IAAMC,YAAY;QAC5CR,KAAK,CAACM,IAAI,GAAG,AAAEE,CAAAA,WAAW,EAAC,KAAM,IAAMC,YAAY;QACnDT,KAAK,CAACM,IAAI,GAAG,AAAEG,CAAAA,WAAW,CAAA,KAAM,IAAMC,WAAW;IACnD;IAEA,OAAOX;AACT,EAAC"}