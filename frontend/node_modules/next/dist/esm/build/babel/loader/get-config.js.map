{"version": 3, "sources": ["../../../../src/build/babel/loader/get-config.ts"], "names": ["readFileSync", "JSON5", "createConfigItem", "loadOptions", "loadConfig", "consumeIterator", "Log", "nextDistPath", "fileExtensionRegex", "getCacheCharacteristics", "loaderOptions", "source", "filename", "isServer", "pagesDir", "isPageFile", "startsWith", "isNextDist", "test", "hasModuleExports", "indexOf", "fileExt", "exec", "getPlugins", "cacheCharacteristics", "hasReactRefresh", "development", "applyCommonJsItem", "require", "type", "reactRefreshItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageConfigItem", "disallowExportAllItem", "transformDefineItem", "resolve", "nextSsgItem", "commonJsItem", "nextFontUnsupported", "filter", "Boolean", "isJsonFile", "isJsFile", "getCustomBabelConfig", "config<PERSON><PERSON><PERSON><PERSON>", "babelConfigRaw", "parse", "Error", "babelConfigWarned", "checkCustomBabelConfigDeprecation", "config", "Object", "keys", "length", "plugins", "presets", "otherOptions", "isPresetReadyToDeprecate", "pluginReasons", "unsupportedPlugins", "Array", "isArray", "plugin", "pluginName", "push", "warn", "join", "getFreshConfig", "target", "inputSourceMap", "hasJsxRuntime", "configFile", "customConfig", "undefined", "options", "babelrc", "cloneInputAst", "sourceMaps", "sourceMap", "sourceFileName", "env", "overrides", "caller", "name", "supportsStaticESM", "supportsDynamicImport", "supportsTopLevelAwait", "isDev", "defineProperty", "enumerable", "writable", "value", "reason", "emitWarning", "loadedOptions", "get<PERSON><PERSON><PERSON><PERSON>", "flags", "config<PERSON><PERSON>", "Map", "configFiles", "Set", "getConfig", "addDependency", "cache<PERSON>ey", "has", "cachedConfig", "get", "cwd", "root", "add", "info", "freshConfig", "call", "set"], "mappings": "AAAA,SAASA,YAAY,QAAQ,KAAI;AACjC,OAAOC,WAAW,2BAA0B;AAE5C,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,gCAA+B;AAC7E,OAAOC,gBAAgB,2CAA0C;AAGjE,SAASC,eAAe,QAAQ,SAAQ;AACxC,YAAYC,SAAS,mBAAkB;AAEvC,MAAMC,eACJ;AA6BF,MAAMC,qBAAqB;AAC3B,SAASC,wBACPC,aAAqC,EACrCC,MAAc,EACdC,QAAgB;QAMAJ;IAJhB,MAAM,EAAEK,QAAQ,EAAEC,QAAQ,EAAE,GAAGJ;IAC/B,MAAMK,aAAaH,SAASI,UAAU,CAACF;IACvC,MAAMG,aAAaV,aAAaW,IAAI,CAACN;IACrC,MAAMO,mBAAmBR,OAAOS,OAAO,CAAC,sBAAsB,CAAC;IAC/D,MAAMC,UAAUb,EAAAA,2BAAAA,mBAAmBc,IAAI,CAACV,8BAAxBJ,wBAAmC,CAAC,EAAE,KAAI;IAE1D,OAAO;QACLK;QACAE;QACAE;QACAE;QACAE;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,WACPb,aAAqC,EACrCc,oBAAqD;IAErD,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAE,GAC1DK;IAEF,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GAAGhB;IAEzC,MAAMiB,oBAAoBR,mBACtBjB,iBAAiB0B,QAAQ,wBAAwB;QAAEC,MAAM;IAAS,KAClE;IACJ,MAAMC,mBAAmBL,kBACrBvB,iBACE;QACE0B,QAAQ;QACR;YAAEG,cAAc;QAAK;KACtB,EACD;QAAEF,MAAM;IAAS,KAEnB;IACJ,MAAMG,iBACJ,CAACnB,YAAYE,aACTb,iBAAiB;QAAC0B,QAAQ;KAA+B,EAAE;QACzDC,MAAM;IACR,KACA;IACN,MAAMI,wBACJ,CAACpB,YAAYE,aACTb,iBACE;QAAC0B,QAAQ;KAAuD,EAChE;QAAEC,MAAM;IAAS,KAEnB;IACN,MAAMK,sBAAsBhC,iBAC1B;QACE0B,QAAQO,OAAO,CAAC;QAChB;YACE,wBAAwBT,cAAc,gBAAgB;YACtD,iBAAiBb,WAAW,cAAc;YAC1C,mBAAmBA,WAAW,QAAQ;QACxC;QACA;KACD,EACD;QAAEgB,MAAM;IAAS;IAEnB,MAAMO,cACJ,CAACvB,YAAYE,aACTb,iBAAiB;QAAC0B,QAAQO,OAAO,CAAC;KAAiC,EAAE;QACnEN,MAAM;IACR,KACA;IACN,MAAMQ,eAAepB,aACjBf,iBACE0B,QAAQ,+DACR;QAAEC,MAAM;IAAS,KAEnB;IACJ,MAAMS,sBAAsBpC,iBAC1B;QAAC0B,QAAQ;KAAoC,EAC7C;QAAEC,MAAM;IAAS;IAGnB,OAAO;QACLC;QACAE;QACAC;QACAN;QACAO;QACAE;QACAC;QACAC;KACD,CAACC,MAAM,CAACC;AACX;AAEA,MAAMC,aAAa;AACnB,MAAMC,WAAW;AAEjB;;;;;CAKC,GACD,SAASC,qBAAqBC,cAAsB;IAClD,IAAIH,WAAWnB,IAAI,CAACsB,iBAAiB;QACnC,MAAMC,iBAAiB7C,aAAa4C,gBAAgB;QACpD,OAAO3C,MAAM6C,KAAK,CAACD;IACrB,OAAO,IAAIH,SAASpB,IAAI,CAACsB,iBAAiB;QACxC,OAAOhB,QAAQgB;IACjB;IACA,MAAM,IAAIG,MACR;AAEJ;AAEA,IAAIC,oBAAoB;AACxB;;;;;CAKC,GACD,SAASC,kCACPC,MAAuC;IAEvC,IAAI,CAACA,UAAUC,OAAOC,IAAI,CAACF,QAAQG,MAAM,KAAK,GAAG;QAC/C;IACF;IAEA,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAGC,cAAc,GAAGN;IAC9C,IAAIC,OAAOC,IAAI,CAACI,gBAAgB,CAAC,GAAGH,MAAM,GAAG,GAAG;QAC9C;IACF;IAEA,IAAIL,mBAAmB;QACrB;IACF;IAEAA,oBAAoB;IAEpB,MAAMS,2BACJ,CAACF,WACDA,QAAQF,MAAM,KAAK,KAClBE,QAAQF,MAAM,KAAK,KAAKE,OAAO,CAAC,EAAE,KAAK;IAC1C,MAAMG,gBAAgB,EAAE;IACxB,MAAMC,qBAAqB,EAAE;IAE7B,IAAIC,MAAMC,OAAO,CAACP,UAAU;QAC1B,KAAK,MAAMQ,UAAUR,QAAS;YAC5B,MAAMS,aAAaH,MAAMC,OAAO,CAACC,UAAUA,MAAM,CAAC,EAAE,GAAGA;YAEvD,wFAAwF;YACxF,6CAA6C;YAC7C,OAAQC;gBACN,KAAK;gBACL,KAAK;oBACHL,cAAcM,IAAI,CAChB,CAAC,0FAA0F,CAAC;oBAE9F;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qFAAqF,CAAC;oBAEzF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,gFAAgF,CAAC;oBAEpF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qGAAqG,CAAC;oBAEzG;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,8FAA8F,CAAC;oBAElG;gBACF;oBACEL,mBAAmBK,IAAI,CAACD;oBACxB;YACJ;QACF;IACF;IAEA,IAAIN,4BAA4BE,mBAAmBN,MAAM,KAAK,GAAG;QAC/D/C,IAAI2D,IAAI,CACN,CAAC,mEAAmE,EAClEP,cAAcL,MAAM,GAAG,IAAI,MAAM,IAClC,CAAC;QAGJ,IAAIK,cAAcL,MAAM,GAAG,GAAG;YAC5B/C,IAAI2D,IAAI,CAAC,CAAC,kDAAkD,CAAC;YAC7D3D,IAAI2D,IAAI,CAACP,cAAcQ,IAAI,CAAC;YAC5B5D,IAAI2D,IAAI,CACN,CAAC,4HAA4H,CAAC;QAElI;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,eAEP3C,oBAAqD,EACrDd,aAAqC,EACrC0D,MAAc,EACdxD,QAAgB,EAChByD,cAA8B;IAE9B,IAAI,EAAExD,QAAQ,EAAEC,QAAQ,EAAEY,WAAW,EAAE4C,aAAa,EAAEC,UAAU,EAAE,GAChE7D;IAEF,IAAI8D,eAAoBD,aACpB5B,qBAAqB4B,cACrBE;IAEJxB,kCAAkCuB;IAElC,IAAIE,UAAU;QACZC,SAAS;QACTC,eAAe;QACfhE;QACAyD,gBAAgBA,kBAAkBI;QAElC,sEAAsE;QACtE,4CAA4C;QAC5CI,YACEnE,cAAcmE,UAAU,KAAKJ,YACzB,IAAI,CAACK,SAAS,GACdpE,cAAcmE,UAAU;QAE9B,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBnE;QAEhB0C,SAAS;eACJ/B,WAAWb,eAAec;eACzBgD,CAAAA,gCAAAA,aAAclB,OAAO,KAAI,EAAE;SAChC;QAED,oCAAoC;QACpCc,QAAQvD,WAAW4D,YAAYD,gCAAAA,aAAcJ,MAAM;QACnD,iCAAiC;QACjCY,GAAG,EAAER,gCAAAA,aAAcQ,GAAG;QAEtBzB,SAAS,AAAC,CAAA;YACR,uEAAuE;YACvE,IAAIiB,gCAAAA,aAAcjB,OAAO,EAAE;gBACzB,OAAOiB,aAAajB,OAAO;YAC7B;YAEA,6EAA6E;YAC7E,IAAIiB,cAAc;gBAChB,OAAOC;YACT;YAEA,mEAAmE;YACnE,OAAO;gBAAC;aAAa;QACvB,CAAA;QAEAQ,WAAWvE,cAAcuE,SAAS;QAElCC,QAAQ;YACNC,MAAM;YACNC,mBAAmB;YACnBC,uBAAuB;YAEvB,oDAAoD;YACpD,mDAAmD;YACnDjB,QAAQA;YAER,gEAAgE;YAChE,sEAAsE;YACtE,sBAAsB;YACtBkB,uBAAuB;YAEvBzE;YACAC;YACAyE,OAAO7D;YACP4C;YAEA,GAAG5D,cAAcwE,MAAM;QACzB;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAOR,QAAQN,MAAM,KAAK,aAAa;QACzC,OAAOM,QAAQN,MAAM;IACvB;IAEAjB,OAAOqC,cAAc,CAACd,QAAQQ,MAAM,EAAE,aAAa;QACjDO,YAAY;QACZC,UAAU;QACVC,OAAO,CAACC;YACN,IAAI,CAAEA,CAAAA,kBAAkB7C,KAAI,GAAI;gBAC9B6C,SAAS,IAAI7C,MAAM6C;YACrB;YACA,IAAI,CAACC,WAAW,CAACD;QACnB;IACF;IAEA,MAAME,gBAAgB3F,YAAYuE;IAClC,MAAMxB,SAAS7C,gBAAgBD,WAAW0F;IAE1C,OAAO5C;AACT;AAEA;;;;CAIC,GACD,SAAS6C,YAAYvE,oBAAqD;IACxE,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAEE,OAAO,EAAE,GACnEG;IAEF,MAAMwE,QACJ,IACCnF,CAAAA,WAAW,IAAS,CAAA,IACpBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,mBAAmB,IAAS,CAAA;IAE/B,OAAOE,UAAU2E;AACnB;AAGA,MAAMC,cAAqC,IAAIC;AAC/C,MAAMC,cAA2B,IAAIC;AAErC,eAAe,SAASC,UAEtB,EACE1F,MAAM,EACNyD,MAAM,EACN1D,aAAa,EACbE,QAAQ,EACRyD,cAAc,EAOf;IAED,MAAM7C,uBAAuBf,wBAC3BC,eACAC,QACAC;IAGF,IAAIF,cAAc6D,UAAU,EAAE;QAC5B,qFAAqF;QACrF,IAAI,CAAC+B,aAAa,CAAC5F,cAAc6D,UAAU;IAC7C;IAEA,MAAMgC,WAAWR,YAAYvE;IAC7B,IAAIyE,YAAYO,GAAG,CAACD,WAAW;QAC7B,MAAME,eAAeR,YAAYS,GAAG,CAACH;QAErC,OAAO;YACL,GAAGE,YAAY;YACf/B,SAAS;gBACP,GAAG+B,aAAa/B,OAAO;gBACvBiC,KAAKjG,cAAciG,GAAG;gBACtBC,MAAMlG,cAAciG,GAAG;gBACvB/F;gBACAmE,gBAAgBnE;YAClB;QACF;IACF;IAEA,IAAIF,cAAc6D,UAAU,IAAI,CAAC4B,YAAYK,GAAG,CAAC9F,cAAc6D,UAAU,GAAG;QAC1E4B,YAAYU,GAAG,CAACnG,cAAc6D,UAAU;QACxCjE,IAAIwG,IAAI,CACN,CAAC,wCAAwC,EAAEpG,cAAc6D,UAAU,CAAC,CAAC;IAEzE;IAEA,MAAMwC,cAAc5C,eAAe6C,IAAI,CACrC,IAAI,EACJxF,sBACAd,eACA0D,QACAxD,UACAyD;IAGF4B,YAAYgB,GAAG,CAACV,UAAUQ;IAE1B,OAAOA;AACT"}