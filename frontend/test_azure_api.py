import requests
import os

# Azure认知服务配置
AZURE_API_KEY = "7Mt2w73ZVyC3Ylqw9RZLWb5yI9I1OWtWKBAE1hd2t8aQxfrMIrcWJQQJ99BIACNns7RXJ3w3AAAHACOGDtyU"
AZURE_ENDPOINT = "https://aegisdetectorservice.cognitiveservices.azure.com/"

def test_azure_api(image_path):
    """测试Azure认知服务API"""
    # 构建Azure API请求
    headers = {
        "Content-Type": "application/octet-stream",
        "Ocp-Apim-Subscription-Key": AZURE_API_KEY
    }
    
    # 图像分析API端点
    analyze_url = f"{AZURE_ENDPOINT}vision/v3.2/analyze?visualFeatures=Adult,Objects,Faces"
    
    # 读取图像文件
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
    
    # 发送请求到Azure
    try:
        response = requests.post(analyze_url, headers=headers, data=image_data)
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析响应
        result = response.json()
        print("Azure API调用成功!")
        print("结果:")
        print(result)
        
        # 分析结果
        adult_score = result.get("adult", {}).get("adultScore", 0)
        is_artificial = result.get("adult", {}).get("isArtificialAdultContent", False)
        faces = result.get("faces", [])
        
        print("\n分析结果:")
        print(f"成人内容分数: {adult_score}")
        print(f"是否人工生成的内容: {is_artificial}")
        print(f"检测到的人脸数量: {len(faces)}")
        
        # 计算篡改可能性分数
        tampering_score = 0.0
        
        # 如果检测到人工生成的成人内容，这很可能是篡改
        if is_artificial:
            tampering_score += 0.7
            print("检测到人工生成的内容，增加篡改可能性分数0.7")
        
        # 根据成人内容分数增加篡改可能性
        tampering_score += adult_score * 0.3
        print(f"根据成人内容分数增加篡改可能性分数{adult_score * 0.3}")
        
        # 如果检测到多个人脸，可能是合成图像
        if len(faces) > 1:
            tampering_score += 0.2
            print(f"检测到多个人脸({len(faces)}个)，增加篡改可能性分数0.2")
        
        # 限制分数在0-1范围内
        tampering_score = min(max(tampering_score, 0.0), 1.0)
        
        print(f"\n最终篡改可能性分数: {tampering_score}")
        print(f"判断结果: {'疑似AI修改' if tampering_score > 0.5 else '未检测到篡改'}")
        
        return result
    except Exception as e:
        print(f"Azure API调用错误: {str(e)}")
        return None

# 测试代码
if __name__ == "__main__":
    # 获取测试图片路径
    test_image = input("请输入测试图片路径: ")
    if os.path.exists(test_image):
        test_azure_api(test_image)
    else:
        print(f"错误: 文件 {test_image} 不存在")
