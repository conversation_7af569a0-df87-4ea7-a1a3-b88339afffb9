/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhanghecheng%2FDownloads%2F%E2%80%9C%E6%85%A7%E7%9C%BC%E2%80%9DAI%E7%AF%A1%E6%94%B9%E5%9B%BE%E7%89%87%E6%A3%80%E6%B5%8B%E5%B9%B3%E5%8F%B0%2Ffrontend%2Fsrc%2Fpages%2Findex.js&page=%2F!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhanghecheng%2FDownloads%2F%E2%80%9C%E6%85%A7%E7%9C%BC%E2%80%9DAI%E7%AF%A1%E6%94%B9%E5%9B%BE%E7%89%87%E6%A3%80%E6%B5%8B%E5%B9%B3%E5%8F%B0%2Ffrontend%2Fsrc%2Fpages%2Findex.js&page=%2F! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.js */ \"./src/pages/index.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRnpoYW5naGVjaGVuZyUyRkRvd25sb2FkcyUyRiVFMiU4MCU5QyVFNiU4NSVBNyVFNyU5QyVCQyVFMiU4MCU5REFJJUU3JUFGJUExJUU2JTk0JUI5JUU1JTlCJUJFJUU3JTg5JTg3JUU2JUEzJTgwJUU2JUI1JThCJUU1JUI5JUIzJUU1JThGJUIwJTJGZnJvbnRlbmQlMkZzcmMlMkZwYWdlcyUyRmluZGV4LmpzJnBhZ2U9JTJGISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGtEQUFzQjtBQUM3QztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/M2JhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2luZGV4LmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhanghecheng%2FDownloads%2F%E2%80%9C%E6%85%A7%E7%9C%BC%E2%80%9DAI%E7%AF%A1%E6%94%B9%E5%9B%BE%E7%89%87%E6%A3%80%E6%B5%8B%E5%B9%B3%E5%8F%B0%2Ffrontend%2Fsrc%2Fpages%2Findex.js&page=%2F!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const prefetchPromise = isAppRouter ? router.prefetch(href, appOptions) : router.prefetch(href, as, options);\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(prefetchPromise).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                forceOptimisticNavigation: !prefetchEnabled,\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * React Component that enables client-side transitions between routes.\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart (e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", {\n        ...restProps,\n        ...childProps\n    }, children);\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/components/ImageUploader.js":
/*!*****************************************!*\
  !*** ./src/components/ImageUploader.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst ImageUploader = ()=>{\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [preview, setPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleFileChange = (e)=>{\n        const selectedFile = e.target.files[0];\n        handleSelectedFile(selectedFile);\n    };\n    const handleSelectedFile = (selectedFile)=>{\n        if (!selectedFile) return;\n        // 检查文件类型\n        if (!selectedFile.type.startsWith(\"image/\")) {\n            setError(\"请选择图片文件\");\n            return;\n        }\n        // 检查文件大小 (限制为10MB)\n        if (selectedFile.size > 10 * 1024 * 1024) {\n            setError(\"图片大小不能超过10MB\");\n            return;\n        }\n        setFile(selectedFile);\n        setError(null);\n        // 创建预览URL\n        const reader = new FileReader();\n        reader.onloadend = ()=>{\n            setPreview(reader.result);\n        };\n        reader.readAsDataURL(selectedFile);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n            handleSelectedFile(e.dataTransfer.files[0]);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!file) {\n            setError(\"请先选择一张图片\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            // 创建FormData对象\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // 调用后端API\n            const response = await fetch(\"http://localhost:8000/api/v1/detection/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 检测成功，跳转到结果页面\n            router.push(\"/detection/result?id=\".concat(data.detection_id));\n        } catch (err) {\n            console.error(\"上传失败:\", err);\n            setError(\"图片上传或检测失败，请稍后再试\");\n            setLoading(false);\n        }\n    };\n    const triggerFileInput = ()=>{\n        fileInputRef.current.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-semibold text-center mb-6\",\n                children: \"上传图片进行AI篡改检测\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors\",\n                        onClick: triggerFileInput,\n                        onDragOver: handleDragOver,\n                        onDrop: handleDrop,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                ref: fileInputRef,\n                                onChange: handleFileChange,\n                                accept: \"image/*\",\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            preview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: preview,\n                                        alt: \"预览图\",\n                                        className: \"max-h-80 mx-auto object-contain mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-sm text-gray-500 mb-2\",\n                                        children: [\n                                            \"已选择: \",\n                                            file.name,\n                                            \" (\",\n                                            (file.size / (1024 * 1024)).toFixed(2),\n                                            \" MB)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-sm text-blue-500\",\n                                        children: \"点击或拖拽更换图片\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-12 h-12 text-gray-400 mb-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-500 font-medium\",\n                                                children: \"点击上传\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \" 或拖拽图片到此处\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"支持JPG, PNG, WEBP等常见图片格式，最大10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg shadow-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: loading || !file,\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"检测中...\"\n                                ]\n                            }, void 0, true) : \"开始检测\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-center text-sm text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"免费用户每天可检测5张图片，\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"text-blue-500 hover:underline\",\n                            children: \"升级为付费用户\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                            lineNumber: 171,\n                            columnNumber: 26\n                        }, undefined),\n                        \"获取更多检测次数和高级功能\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/ImageUploader.js\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageUploader, \"zZrlu31moymnU+YLGwvvhOi4GGk=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ImageUploader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ImageUploader);\nvar _c;\n$RefreshReg$(_c, \"ImageUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ImageUploader.js\n"));

/***/ }),

/***/ "./src/components/Navbar.js":
/*!**********************************!*\
  !*** ./src/components/Navbar.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst Navbar = ()=>{\n    _s();\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 检查用户是否已登录\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            setIsLoggedIn(true);\n            // 这里可以添加获取用户信息的API调用\n            // 暂时使用模拟数据\n            setUserName(\"用户\");\n        }\n    }, []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        setIsLoggedIn(false);\n        setUserName(\"\");\n        router.push(\"/\");\n    };\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"text-xl font-bold text-blue-600\",\n                                children: \"慧眼\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/pricing\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"价格\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/about\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"关于我们\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"欢迎，\",\n                                                        userName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"ml-1 w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"我的仪表盘\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard/history\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"检测历史\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard/settings\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"账户设置\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"退出登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                            children: \"登录\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors\",\n                                            children: \"注册\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleMenu,\n                                className: \"text-gray-700 focus:outline-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                        lineNumber: 79,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                            children: \"首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/pricing\",\n                            className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                            children: \"价格\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                            children: \"关于我们\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, undefined),\n                        isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                                    children: \"我的仪表盘\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard/history\",\n                                    className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                                    children: \"检测历史\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard/settings\",\n                                    className: \"block py-2 text-gray-700 hover:text-blue-600\",\n                                    children: \"账户设置\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"block w-full text-left py-2 text-gray-700 hover:text-blue-600\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/login\",\n                                    className: \"block text-center py-2 text-gray-700 hover:text-blue-600 border border-gray-300 rounded-md\",\n                                    children: \"登录\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/register\",\n                                    className: \"block text-center py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md\",\n                                    children: \"注册\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/components/Navbar.js\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"RzDrVxfEfGg82u4T9zjVpEFR/rY=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.js\n"));

/***/ }),

/***/ "./src/pages/index.js":
/*!****************************!*\
  !*** ./src/pages/index.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Navbar */ \"./src/components/Navbar.js\");\n/* harmony import */ var _components_ImageUploader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ImageUploader */ \"./src/components/ImageUploader.js\");\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"慧眼 - AI篡改图片检测平台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"使用先进的AI技术检测图片是否被篡改\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"慧眼 - AI篡改图片检测平台\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"使用先进的人工智能技术，快速准确地检测图片是否被AI工具篡改或生成\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUploader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 grid md:grid-cols-3 gap-8 max-w-5xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"高精度检测\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 32,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"采用最新的AI技术，能够精确识别出被篡改的图片，准确率高达95%以上\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 text-4xl mb-4\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"快速响应\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"强大的云端处理能力，上传图片后秒级返回检测结果，无需长时间等待\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDD12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"安全可靠\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"所有上传的图片均加密存储，检测完成后自动删除，保障您的数据安全\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-100 mt-16 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2023 慧眼 AI篡改图片检测平台. 保留所有权利.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/index.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.js\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ }),

/***/ "./node_modules/next/router.js":
/*!*************************************!*\
  !*** ./node_modules/next/router.js ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/router */ \"./node_modules/next/dist/client/router.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9yb3V0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkdBQWdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L3JvdXRlci5qcz8xYmI2Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9yb3V0ZXInKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/router.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhanghecheng%2FDownloads%2F%E2%80%9C%E6%85%A7%E7%9C%BC%E2%80%9DAI%E7%AF%A1%E6%94%B9%E5%9B%BE%E7%89%87%E6%A3%80%E6%B5%8B%E5%B9%B3%E5%8F%B0%2Ffrontend%2Fsrc%2Fpages%2Findex.js&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);