"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/detection/result",{

/***/ "./src/pages/detection/result.js":
/*!***************************************!*\
  !*** ./src/pages/detection/result.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DetectionResult; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Navbar */ \"./src/components/Navbar.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DetectionResult() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 只有当id存在时才获取数据\n        if (!id) return;\n        // 从后端API获取检测结果\n        const fetchResult = async ()=>{\n            try {\n                setLoading(true);\n                console.log(\"正在获取检测结果，ID: \".concat(id));\n                console.log(\"请求URL: http://localhost:8000/api/v1/detection/result/\".concat(id));\n                // 调用后端API获取检测结果\n                const response = await fetch(\"http://localhost:8000/api/v1/detection/result/\".concat(id));\n                console.log(\"API响应状态: \".concat(response.status));\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error(\"API错误响应: \".concat(errorText));\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const apiData = await response.json();\n                console.log(\"API响应数据:\", apiData);\n                if (apiData.status !== \"success\") {\n                    throw new Error(apiData.message || \"获取检测结果失败\");\n                }\n                // 转换API数据格式为前端需要的格式\n                const result = {\n                    id: apiData.data.file_id,\n                    originalImage: \"http://localhost:8000/api/v1/detection/image/\".concat(apiData.data.file_id),\n                    heatmapImage: apiData.data.heatmap_url ? \"http://localhost:8000\".concat(apiData.data.heatmap_url) : null,\n                    isAiGenerated: apiData.data.is_fake,\n                    confidence: apiData.data.confidence * 100,\n                    detectionTime: apiData.data.detection_time,\n                    processingTimeMs: null // 后端API没有提供这个信息\n                };\n                setResult(result);\n                setLoading(false);\n            } catch (err) {\n                console.error(\"获取检测结果失败:\", err);\n                // 提供更详细的错误信息\n                let errorMessage = \"获取检测结果失败，请稍后再试\";\n                if (err.message.includes(\"Failed to fetch\")) {\n                    errorMessage = \"无法连接到服务器，请检查网络连接或确保后端服务正在运行\";\n                } else if (err.message.includes(\"404\")) {\n                    errorMessage = \"检测记录不存在，请重新上传图片\";\n                } else if (err.message.includes(\"500\")) {\n                    errorMessage = \"服务器内部错误，请稍后再试\";\n                } else if (err.message) {\n                    errorMessage = \"错误: \".concat(err.message);\n                }\n                setError(errorMessage);\n                setLoading(false);\n            }\n        };\n        fetchResult();\n    }, [\n        id\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    if (!result) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                            children: \"未找到检测结果\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"检测结果 - 慧眼AI篡改图片检测平台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI图片篡改检测结果\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-center mb-6\",\n                            children: \"检测结果\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 rounded-lg border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"检测概要\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                \"检测ID: \",\n                                                result.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"AI生成/篡改可能性:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold \".concat(result.isAiGenerated ? \"text-red-600\" : \"text-green-600\"),\n                                                                    children: result.isAiGenerated ? \"极有可能\" : \"极不可能\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2.5 mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 rounded-full \".concat(result.isAiGenerated ? \"bg-red-600\" : \"bg-green-600\"),\n                                                                style: {\n                                                                    width: \"\".concat(result.confidence, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right text-sm mt-1\",\n                                                            children: [\n                                                                \"置信度: \",\n                                                                result.confidence.toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"检测时间:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: new Date(result.detectionTime).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                result.processingTimeMs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"处理耗时:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                result.processingTimeMs,\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"检测结果解释:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-4\",\n                                                    children: result.isAiGenerated ? \"我们的AI系统检测到此图片极有可能是AI生成或被篡改过的。热力图中红色区域表示可能被篡改的部分。\" : \"我们的AI系统未检测到此图片有明显的AI生成或篡改痕迹。\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                result.isAiGenerated && result.detectedAreas && result.detectedAreas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"可疑区域:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc pl-5\",\n                                                            children: result.detectedAreas.map((area, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: [\n                                                                        \"区域 \",\n                                                                        index + 1,\n                                                                        \": 置信度 \",\n                                                                        area.confidence.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"原始图片\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: result.originalImage,\n                                                alt: \"原始图片\",\n                                                className: \"w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                result.isAiGenerated && result.heatmapImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"热力图分析\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: result.heatmapImage,\n                                                alt: \"热力图分析\",\n                                                className: \"w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: \"热力图中红色区域表示可能被AI篡改的部分，颜色越深表示可能性越高\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg mr-4\",\n                                    children: \"返回首页\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.print(),\n                                    className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-lg\",\n                                    children: \"打印结果\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(DetectionResult, \"mTZGuLVOtyCH5LwQDCzX4QrwT5Q=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DetectionResult;\nvar _c;\n$RefreshReg$(_c, \"DetectionResult\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/detection/result.js\n"));

/***/ })

});