"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/detection/result",{

/***/ "./src/pages/detection/result.js":
/*!***************************************!*\
  !*** ./src/pages/detection/result.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DetectionResult; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Navbar */ \"./src/components/Navbar.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DetectionResult() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 只有当id存在时才获取数据\n        if (!id) return;\n        // 从后端API获取检测结果\n        const fetchResult = async ()=>{\n            try {\n                setLoading(true);\n                // 调用后端API获取检测结果\n                const response = await fetch(\"http://localhost:8000/api/v1/detection/result/\".concat(id));\n                if (!response.ok) {\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const apiData = await response.json();\n                if (apiData.status !== \"success\") {\n                    throw new Error(apiData.message || \"获取检测结果失败\");\n                }\n                // 转换API数据格式为前端需要的格式\n                const result = {\n                    id: apiData.data.file_id,\n                    originalImage: \"http://localhost:8000/api/v1/detection/image/\".concat(apiData.data.file_id),\n                    heatmapImage: apiData.data.heatmap_url ? \"http://localhost:8000\".concat(apiData.data.heatmap_url) : null,\n                    isAiGenerated: apiData.data.is_fake,\n                    confidence: apiData.data.confidence * 100,\n                    detectionTime: apiData.data.detection_time,\n                    processingTimeMs: null // 后端API没有提供这个信息\n                };\n                setResult(result);\n                setLoading(false);\n            } catch (err) {\n                console.error(\"获取检测结果失败:\", err);\n                // 提供更详细的错误信息\n                let errorMessage = \"获取检测结果失败，请稍后再试\";\n                if (err.message.includes(\"Failed to fetch\")) {\n                    errorMessage = \"无法连接到服务器，请检查网络连接或确保后端服务正在运行\";\n                } else if (err.message.includes(\"404\")) {\n                    errorMessage = \"检测记录不存在，请重新上传图片\";\n                } else if (err.message.includes(\"500\")) {\n                    errorMessage = \"服务器内部错误，请稍后再试\";\n                } else if (err.message) {\n                    errorMessage = \"错误: \".concat(err.message);\n                }\n                setError(errorMessage);\n                setLoading(false);\n            }\n        };\n        fetchResult();\n    }, [\n        id\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this);\n    }\n    if (!result) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                            children: \"未找到检测结果\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"检测结果 - 慧眼AI篡改图片检测平台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI图片篡改检测结果\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-center mb-6\",\n                            children: \"检测结果\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 rounded-lg border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"检测概要\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                \"检测ID: \",\n                                                result.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"AI生成/篡改可能性:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold \".concat(result.isAiGenerated ? \"text-red-600\" : \"text-green-600\"),\n                                                                    children: result.isAiGenerated ? \"极有可能\" : \"极不可能\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2.5 mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 rounded-full \".concat(result.isAiGenerated ? \"bg-red-600\" : \"bg-green-600\"),\n                                                                style: {\n                                                                    width: \"\".concat(result.confidence, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right text-sm mt-1\",\n                                                            children: [\n                                                                \"置信度: \",\n                                                                result.confidence.toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"检测时间:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: new Date(result.detectionTime).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                result.processingTimeMs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"处理耗时:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                result.processingTimeMs,\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"检测结果解释:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-4\",\n                                                    children: result.isAiGenerated ? \"我们的AI系统检测到此图片极有可能是AI生成或被篡改过的。热力图中红色区域表示可能被篡改的部分。\" : \"我们的AI系统未检测到此图片有明显的AI生成或篡改痕迹。\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                result.isAiGenerated && result.detectedAreas && result.detectedAreas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"可疑区域:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc pl-5\",\n                                                            children: result.detectedAreas.map((area, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: [\n                                                                        \"区域 \",\n                                                                        index + 1,\n                                                                        \": 置信度 \",\n                                                                        area.confidence.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"原始图片\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: result.originalImage,\n                                                alt: \"原始图片\",\n                                                className: \"w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                result.isAiGenerated && result.heatmapImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"热力图分析\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: result.heatmapImage,\n                                                alt: \"热力图分析\",\n                                                className: \"w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: \"热力图中红色区域表示可能被AI篡改的部分，颜色越深表示可能性越高\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg mr-4\",\n                                    children: \"返回首页\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.print(),\n                                    className: \"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-lg\",\n                                    children: \"打印结果\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/“慧眼”AI篡改图片检测平台/frontend/src/pages/detection/result.js\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(DetectionResult, \"mTZGuLVOtyCH5LwQDCzX4QrwT5Q=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DetectionResult;\nvar _c;\n$RefreshReg$(_c, \"DetectionResult\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/detection/result.js\n"));

/***/ })

});