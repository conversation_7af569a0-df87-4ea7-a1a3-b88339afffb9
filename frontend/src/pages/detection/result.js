import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Navbar from '../../components/Navbar';

export default function DetectionResult() {
  const router = useRouter();
  const { id } = router.query;
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 只有当id存在时才获取数据
    if (!id) return;

    // 从后端API获取检测结果
    const fetchResult = async () => {
      try {
        setLoading(true);

        // 调用后端API获取检测结果
        const response = await fetch(`http://localhost:8000/api/v1/detection/result/${id}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const apiData = await response.json();

        if (apiData.status !== 'success') {
          throw new Error(apiData.message || '获取检测结果失败');
        }

        // 转换API数据格式为前端需要的格式
        const result = {
          id: apiData.data.file_id,
          originalImage: `http://localhost:8000/api/v1/detection/image/${apiData.data.file_id}`,
          heatmapImage: apiData.data.heatmap_url ? `http://localhost:8000${apiData.data.heatmap_url}` : null,
          isAiGenerated: apiData.data.is_fake,
          confidence: apiData.data.confidence * 100, // 转换为百分比
          detectionTime: apiData.data.detection_time,
          processingTimeMs: null // 后端API没有提供这个信息
        };

        setResult(result);
        setLoading(false);
      } catch (err) {
        console.error('获取检测结果失败:', err);
        setError('获取检测结果失败，请稍后再试');
        setLoading(false);
      }
    };

    fetchResult();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
          <button 
            onClick={() => router.push('/')}
            className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
            未找到检测结果
          </div>
          <button 
            onClick={() => router.push('/')}
            className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>检测结果 - 慧眼AI篡改图片检测平台</title>
        <meta name="description" content="AI图片篡改检测结果" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-center mb-6">检测结果</h1>
          
          <div className="mb-6 p-4 rounded-lg border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">检测概要</h2>
              <span className="text-sm text-gray-500">检测ID: {result.id}</span>
            </div>
            
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="mb-4">
                  <div className="flex justify-between">
                    <span className="font-medium">AI生成/篡改可能性:</span>
                    <span className={`font-bold ${result.isAiGenerated ? 'text-red-600' : 'text-green-600'}`}>
                      {result.isAiGenerated ? '极有可能' : '极不可能'}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                    <div 
                      className={`h-2.5 rounded-full ${result.isAiGenerated ? 'bg-red-600' : 'bg-green-600'}`}
                      style={{ width: `${result.confidence}%` }}
                    ></div>
                  </div>
                  <div className="text-right text-sm mt-1">
                    置信度: {result.confidence.toFixed(2)}%
                  </div>
                </div>
                
                <div className="mb-4">
                  <p className="font-medium mb-2">检测时间:</p>
                  <p className="text-gray-700">{new Date(result.detectionTime).toLocaleString()}</p>
                </div>
                
                {result.processingTimeMs && (
                  <div className="mb-4">
                    <p className="font-medium mb-2">处理耗时:</p>
                    <p className="text-gray-700">{result.processingTimeMs}ms</p>
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <p className="font-medium mb-2">检测结果解释:</p>
                <p className="text-gray-700 mb-4">
                  {result.isAiGenerated 
                    ? '我们的AI系统检测到此图片极有可能是AI生成或被篡改过的。热力图中红色区域表示可能被篡改的部分。' 
                    : '我们的AI系统未检测到此图片有明显的AI生成或篡改痕迹。'}
                </p>
                
                {result.isAiGenerated && result.detectedAreas && result.detectedAreas.length > 0 && (
                  <div>
                    <p className="font-medium mb-2">可疑区域:</p>
                    <ul className="list-disc pl-5">
                      {result.detectedAreas.map((area, index) => (
                        <li key={index} className="text-gray-700">
                          区域 {index + 1}: 置信度 {area.confidence.toFixed(1)}%
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">原始图片</h3>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <img 
                  src={result.originalImage} 
                  alt="原始图片" 
                  className="w-full h-auto"
                />
              </div>
            </div>
            
            {result.isAiGenerated && result.heatmapImage && (
              <div>
                <h3 className="text-lg font-semibold mb-3">热力图分析</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <img 
                    src={result.heatmapImage} 
                    alt="热力图分析" 
                    className="w-full h-auto"
                  />
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  热力图中红色区域表示可能被AI篡改的部分，颜色越深表示可能性越高
                </p>
              </div>
            )}
          </div>
          
          <div className="mt-8 flex justify-center">
            <button 
              onClick={() => router.push('/')}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg mr-4"
            >
              返回首页
            </button>
            <button 
              onClick={() => window.print()}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-lg"
            >
              打印结果
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
