import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Navbar from '../components/Navbar';

export default function Pricing() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentPlan, setCurrentPlan] = useState('free');
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (token) {
      setIsLoggedIn(true);
      
      // 模拟获取用户当前订阅计划
      // 在实际实现中，这里应该调用后端API
      setCurrentPlan('free'); // 假设当前是免费计划
    }
  }, []);

  const handleSubscribe = (plan) => {
    if (!isLoggedIn) {
      // 未登录用户重定向到登录页面
      router.push(`/auth/login?redirect=pricing`);
      return;
    }

    // 模拟订阅流程
    // 在实际实现中，这里应该重定向到支付页面或调用支付API
    alert(`您选择了${plan}计划，即将跳转到支付页面`);
    
    // 模拟支付成功后的处理
    // router.push('/dashboard');
  };

  const plans = [
    {
      id: 'free',
      name: '免费版',
      price: '¥0',
      period: '永久免费',
      description: '适合个人用户基础使用',
      features: [
        '每日5次免费检测',
        '基础检测报告',
        '检测结果保存7天',
        '标准客户支持'
      ],
      buttonText: '当前计划',
      popular: false
    },
    {
      id: 'pro',
      name: '专业版',
      price: '¥99',
      period: '每月',
      description: '适合专业用户和小型团队',
      features: [
        '每日50次检测',
        '详细检测报告',
        '热力图分析',
        '检测结果保存30天',
        '优先客户支持'
      ],
      buttonText: '升级到专业版',
      popular: true
    },
    {
      id: 'enterprise',
      name: '企业版',
      price: '¥299',
      period: '每月',
      description: '适合企业和大型团队',
      features: [
        '无限次检测',
        '高级检测报告',
        '热力图和区域分析',
        '批量检测功能',
        'API访问',
        '检测结果永久保存',
        '专属客户经理'
      ],
      buttonText: '升级到企业版',
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>价格方案 - 慧眼AI篡改图片检测平台</title>
        <meta name="description" content="选择适合您需求的价格方案" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />
      
      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">选择适合您的方案</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们提供多种灵活的价格方案，满足不同用户的需求。无论您是个人用户还是企业团队，都能找到合适的选择。
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <div 
              key={plan.id} 
              className={`bg-white rounded-lg shadow-lg overflow-hidden border ${plan.popular ? 'border-blue-500' : 'border-gray-200'} transform transition-transform duration-300 hover:scale-105`}
            >
              {plan.popular && (
                <div className="bg-blue-500 text-white text-center py-2 font-medium">
                  最受欢迎
                </div>
              )}
              
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div className="flex items-baseline mb-4">
                  <span className="text-4xl font-extrabold text-gray-900">{plan.price}</span>
                  <span className="text-gray-500 ml-2">{plan.period}</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <button
                  onClick={() => handleSubscribe(plan.name)}
                  disabled={currentPlan === plan.id}
                  className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${currentPlan === plan.id ? 'bg-gray-300 text-gray-700 cursor-not-allowed' : plan.popular ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-800 hover:bg-gray-900 text-white'}`}
                >
                  {currentPlan === plan.id ? '当前计划' : plan.buttonText}
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">常见问题</h2>
          <div className="max-w-3xl mx-auto">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">如何选择合适的方案？</h3>
              <p className="text-gray-600">根据您的使用频率和需求选择。个人用户可选择免费版，专业用户和小型团队可选择专业版，大型企业可选择企业版。</p>
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">可以随时更改我的订阅计划吗？</h3>
              <p className="text-gray-600">是的，您可以随时升级或降级您的订阅计划。升级将立即生效，降级将在当前计费周期结束后生效。</p>
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">支持哪些支付方式？</h3>
              <p className="text-gray-600">我们支持支付宝、微信支付、银联和主流信用卡等多种支付方式。</p>
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">如何获取发票？</h3>
              <p className="text-gray-600">付款成功后，您可以在账户设置中申请开具电子发票或纸质发票。</p>
            </div>
          </div>
          
          <div className="mt-8">
            <p className="text-gray-600 mb-4">还有其他问题？</p>
            <Link href="/contact" className="text-blue-600 hover:underline font-medium">
              联系我们的客服团队
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
