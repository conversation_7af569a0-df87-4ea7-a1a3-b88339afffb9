import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Navbar from '../../components/Navbar';

export default function AccountSettings() {
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeTab, setActiveTab] = useState('profile'); // profile, password, subscription
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=dashboard/settings');
      return;
    }

    // 获取用户信息
    const fetchUserData = async () => {
      try {
        setLoading(true);
        
        // 模拟API调用
        // 在实际实现中，这里应该调用后端API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟用户数据
        const userData = {
          id: '123',
          name: '张三',
          email: '<EMAIL>',
          createdAt: '2023-01-15T08:30:00Z',
          plan: 'free',
          subscription: {
            type: 'free',
            expiresAt: null,
            features: [
              { name: '每日免费检测次数', value: '5次' },
              { name: '检测结果保存时间', value: '7天' },
              { name: '热力图分析', value: '否' },
              { name: '批量检测', value: '否' },
              { name: 'API访问', value: '否' }
            ]
          }
        };
        
        setUser(userData);
        setFormData({
          name: userData.name,
          email: userData.email,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        
        setLoading(false);
      } catch (err) {
        console.error('获取用户数据失败:', err);
        setError('获取用户数据失败，请稍后再试');
        setLoading(false);
      }
    };

    fetchUserData();
  }, [router]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setError(null);
    setSuccess(null);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新用户对象
      setUser(prev => ({
        ...prev,
        name: formData.name,
        email: formData.email
      }));
      
      setSuccess('个人资料已成功更新');
    } catch (err) {
      console.error('更新个人资料失败:', err);
      setError('更新个人资料失败，请稍后再试');
    } finally {
      setUpdating(false);
    }
  };

  const handlePasswordUpdate = async (e) => {
    e.preventDefault();
    
    // 验证密码
    if (formData.newPassword !== formData.confirmPassword) {
      setError('两次输入的新密码不匹配');
      return;
    }
    
    if (formData.newPassword.length < 8) {
      setError('新密码长度必须至少为8个字符');
      return;
    }
    
    setUpdating(true);
    setError(null);
    setSuccess(null);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 重置密码字段
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
      
      setSuccess('密码已成功更新');
    } catch (err) {
      console.error('更新密码失败:', err);
      setError('更新密码失败，请确认当前密码是否正确');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error && !success) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
          <button 
            onClick={() => router.push('/dashboard')}
            className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            返回仪表盘
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>账户设置 - 慧眼AI篡改图片检测平台</title>
        <meta name="description" content="管理您的账户设置" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">账户设置</h1>
          <Link href="/dashboard" className="text-blue-600 hover:underline">
            返回仪表盘
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* 标签页导航 */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${activeTab === 'profile' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              >
                个人资料
              </button>
              <button
                onClick={() => setActiveTab('password')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${activeTab === 'password' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              >
                修改密码
              </button>
              <button
                onClick={() => setActiveTab('subscription')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${activeTab === 'subscription' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              >
                订阅计划
              </button>
            </nav>
          </div>
          
          <div className="p-6">
            {/* 成功消息 */}
            {success && (
              <div className="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                {success}
              </div>
            )}
            
            {/* 错误消息 */}
            {error && (
              <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                {error}
              </div>
            )}
            
            {/* 个人资料表单 */}
            {activeTab === 'profile' && (
              <form onSubmit={handleProfileUpdate}>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 text-sm font-medium mb-2">姓名</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="email" className="block text-gray-700 text-sm font-medium mb-2">邮箱地址</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div className="mt-6">
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
                    disabled={updating}
                  >
                    {updating ? '更新中...' : '保存更改'}
                  </button>
                </div>
              </form>
            )}
            
            {/* 修改密码表单 */}
            {activeTab === 'password' && (
              <form onSubmit={handlePasswordUpdate}>
                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block text-gray-700 text-sm font-medium mb-2">当前密码</label>
                  <input
                    type="password"
                    id="currentPassword"
                    name="currentPassword"
                    value={formData.currentPassword}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="newPassword" className="block text-gray-700 text-sm font-medium mb-2">新密码</label>
                  <input
                    type="password"
                    id="newPassword"
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    minLength="8"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">密码至少需要8个字符</p>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="confirmPassword" className="block text-gray-700 text-sm font-medium mb-2">确认新密码</label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    minLength="8"
                    required
                  />
                </div>
                
                <div className="mt-6">
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
                    disabled={updating}
                  >
                    {updating ? '更新中...' : '更新密码'}
                  </button>
                </div>
              </form>
            )}
            
            {/* 订阅计划信息 */}
            {activeTab === 'subscription' && (
              <div>
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold">当前计划</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${user.subscription.type === 'premium' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {user.subscription.type === 'premium' ? '高级会员' : '免费用户'}
                    </span>
                  </div>
                  
                  {user.subscription.expiresAt && (
                    <p className="text-sm text-gray-600 mb-4">
                      有效期至: {new Date(user.subscription.expiresAt).toLocaleDateString()}
                    </p>
                  )}
                  
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">包含功能:</h4>
                    <ul className="space-y-2">
                      {user.subscription.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          <span className="text-gray-700">{feature.name}: <span className="font-medium">{feature.value}</span></span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className="flex justify-center">
                  <Link href="/pricing" className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                    {user.subscription.type === 'premium' ? '管理订阅' : '升级到高级版'}
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
