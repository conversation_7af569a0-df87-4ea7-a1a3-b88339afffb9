import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Navbar from '../../components/Navbar';

export default function Dashboard() {
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({
    totalDetections: 0,
    detectionToday: 0,
    remainingFreeDetections: 5,
    isPremium: false
  });
  const [recentDetections, setRecentDetections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=dashboard');
      return;
    }

    // 获取用户信息和统计数据
    const fetchUserData = async () => {
      try {
        setLoading(true);
        
        // 模拟API调用
        // 在实际实现中，这里应该调用后端API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟用户数据
        setUser({
          id: '123',
          name: '张三',
          email: '<EMAIL>',
          createdAt: '2023-01-15T08:30:00Z',
          plan: 'free'
        });
        
        // 模拟统计数据
        setStats({
          totalDetections: 27,
          detectionToday: 3,
          remainingFreeDetections: 2,
          isPremium: false
        });
        
        // 模拟最近检测记录
        setRecentDetections([
          {
            id: 'det_001',
            imageName: '家庭照片.jpg',
            detectionTime: '2023-06-10T14:23:45Z',
            result: true,
            confidence: 92.5
          },
          {
            id: 'det_002',
            imageName: '风景照片.jpg',
            detectionTime: '2023-06-09T10:15:30Z',
            result: false,
            confidence: 98.2
          },
          {
            id: 'det_003',
            imageName: '会议照片.jpg',
            detectionTime: '2023-06-08T16:45:12Z',
            result: true,
            confidence: 87.3
          }
        ]);
        
        setLoading(false);
      } catch (err) {
        console.error('获取用户数据失败:', err);
        setError('获取用户数据失败，请稍后再试');
        setLoading(false);
      }
    };

    fetchUserData();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
          <button 
            onClick={() => router.push('/')}
            className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>用户仪表盘 - 慧眼AI篡改图片检测平台</title>
        <meta name="description" content="慧眼AI篡改图片检测平台用户仪表盘" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">欢迎回来，{user?.name}</h1>
          <p className="text-gray-600">查看您的检测统计和最近活动</p>
        </div>
        
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">总检测次数</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.totalDetections}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">今日检测</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.detectionToday}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">剩余免费检测</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.remainingFreeDetections}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">账户类型</h3>
            <p className="text-xl font-medium">
              {stats.isPremium ? (
                <span className="text-green-600">高级会员</span>
              ) : (
                <span className="text-gray-600">免费用户</span>
              )}
            </p>
            {!stats.isPremium && (
              <Link href="/pricing" className="text-sm text-blue-600 hover:underline mt-2 inline-block">
                升级账户
              </Link>
            )}
          </div>
        </div>
        
        {/* 快速操作 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">快速操作</h2>
          <div className="flex flex-wrap gap-4">
            <Link href="/" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
              新建检测
            </Link>
            <Link href="/dashboard/history" className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition-colors">
              查看历史记录
            </Link>
            <Link href="/dashboard/settings" className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition-colors">
              账户设置
            </Link>
          </div>
        </div>
        
        {/* 最近检测 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">最近检测</h2>
            <Link href="/dashboard/history" className="text-sm text-blue-600 hover:underline">
              查看全部
            </Link>
          </div>
          
          {recentDetections.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      图片名称
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      检测时间
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      检测结果
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      置信度
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentDetections.map((detection) => (
                    <tr key={detection.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {detection.imageName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(detection.detectionTime).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${detection.result ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                          {detection.result ? 'AI生成/篡改' : '真实图片'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {detection.confidence.toFixed(1)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link href={`/detection/result?id=${detection.id}`} className="text-blue-600 hover:text-blue-900">
                          查看详情
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>暂无检测记录</p>
              <Link href="/" className="mt-2 inline-block text-blue-600 hover:underline">
                立即开始检测
              </Link>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
