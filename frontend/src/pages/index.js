import { useState } from 'react';
import Head from 'next/head';
import Navbar from '../components/Navbar';
import ImageUploader from '../components/ImageUploader';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>慧眼 - AI篡改图片检测平台</title>
        <meta name="description" content="使用先进的AI技术检测图片是否被篡改" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">慧眼 - AI篡改图片检测平台</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            使用先进的人工智能技术，快速准确地检测图片是否被AI工具篡改或生成
          </p>
        </div>

        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
          <ImageUploader />
        </div>

        <div className="mt-16 grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-4xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2">高精度检测</h3>
            <p className="text-gray-600">采用最新的AI技术，能够精确识别出被篡改的图片，准确率高达95%以上</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-4xl mb-4">⚡</div>
            <h3 className="text-xl font-semibold mb-2">快速响应</h3>
            <p className="text-gray-600">强大的云端处理能力，上传图片后秒级返回检测结果，无需长时间等待</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-4xl mb-4">🔒</div>
            <h3 className="text-xl font-semibold mb-2">安全可靠</h3>
            <p className="text-gray-600">所有上传的图片均加密存储，检测完成后自动删除，保障您的数据安全</p>
          </div>
        </div>
      </main>

      <footer className="bg-gray-100 mt-16 py-8">
        <div className="container mx-auto px-4 text-center text-gray-500">
          <p>© 2023 慧眼 AI篡改图片检测平台. 保留所有权利.</p>
        </div>
      </footer>
    </div>
  );
}
