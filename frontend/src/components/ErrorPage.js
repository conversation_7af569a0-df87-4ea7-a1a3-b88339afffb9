import React from 'react';
import Link from 'next/link';

const ErrorPage = ({ 
  statusCode = 500, 
  title = '出错了', 
  message = '应用程序遇到了意外错误',
  showHomeButton = true,
  showReloadButton = true 
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <div className="text-center">
          <div className="text-6xl font-bold text-gray-300 mb-4">
            {statusCode}
          </div>
          
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {title}
          </h1>
          
          <p className="text-gray-600 mb-6">
            {message}
          </p>

          <div className="space-y-3">
            {showReloadButton && (
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                刷新页面
              </button>
            )}
            
            {showHomeButton && (
              <Link href="/">
                <button className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  返回首页
                </button>
              </Link>
            )}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              需要帮助？请联系我们的 
              <Link href="/contact" className="text-blue-600 hover:underline">
                客服团队
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
