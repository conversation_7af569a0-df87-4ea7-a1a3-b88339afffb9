import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const Navbar = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (token) {
      setIsLoggedIn(true);
      // 这里可以添加获取用户信息的API调用
      // 暂时使用模拟数据
      setUserName('用户');
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    setIsLoggedIn(false);
    setUserName('');
    router.push('/');
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-blue-600">慧眼</Link>
          </div>
          
          {/* 桌面导航 */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-blue-600 transition-colors">首页</Link>
            <Link href="/pricing" className="text-gray-700 hover:text-blue-600 transition-colors">价格</Link>
            <Link href="/about" className="text-gray-700 hover:text-blue-600 transition-colors">关于我们</Link>
            
            {isLoggedIn ? (
              <div className="relative group">
                <button className="flex items-center text-gray-700 hover:text-blue-600 focus:outline-none">
                  <span>欢迎，{userName}</span>
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                  <Link href="/dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">我的仪表盘</Link>
                  <Link href="/dashboard/history" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">检测历史</Link>
                  <Link href="/dashboard/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账户设置</Link>
                  <button onClick={handleLogout} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/auth/login" className="text-gray-700 hover:text-blue-600 transition-colors">登录</Link>
                <Link href="/auth/register" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                  注册
                </Link>
              </div>
            )}
          </div>
          
          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <button onClick={toggleMenu} className="text-gray-700 focus:outline-none">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                )}
              </svg>
            </button>
          </div>
        </div>
        
        {/* 移动端导航菜单 */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <Link href="/" className="block py-2 text-gray-700 hover:text-blue-600">
              首页
            </Link>
            <Link href="/pricing" className="block py-2 text-gray-700 hover:text-blue-600">
              价格
            </Link>
            <Link href="/about" className="block py-2 text-gray-700 hover:text-blue-600">
              关于我们
            </Link>
            
            {isLoggedIn ? (
              <>
                <Link href="/dashboard" className="block py-2 text-gray-700 hover:text-blue-600">
                  我的仪表盘
                </Link>
                <Link href="/dashboard/history" className="block py-2 text-gray-700 hover:text-blue-600">
                  检测历史
                </Link>
                <Link href="/dashboard/settings" className="block py-2 text-gray-700 hover:text-blue-600">
                  账户设置
                </Link>
                <button 
                  onClick={handleLogout} 
                  className="block w-full text-left py-2 text-gray-700 hover:text-blue-600"
                >
                  退出登录
                </button>
              </>
            ) : (
              <div className="mt-4 space-y-2">
                <Link href="/auth/login" className="block text-center py-2 text-gray-700 hover:text-blue-600 border border-gray-300 rounded-md">
                  登录
                </Link>
                <Link href="/auth/register" className="block text-center py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                  注册
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
