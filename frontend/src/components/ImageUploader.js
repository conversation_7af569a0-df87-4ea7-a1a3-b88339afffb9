import { useState, useRef } from 'react';
import { useRouter } from 'next/router';

const ImageUploader = () => {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);
  const router = useRouter();

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    handleSelectedFile(selectedFile);
  };

  const handleSelectedFile = (selectedFile) => {
    if (!selectedFile) return;

    // 检查文件类型
    if (!selectedFile.type.startsWith('image/')) {
      setError('请选择图片文件');
      return;
    }

    // 检查文件大小 (限制为10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError('图片大小不能超过10MB');
      return;
    }

    setFile(selectedFile);
    setError(null);

    // 创建预览URL
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreview(reader.result);
    };
    reader.readAsDataURL(selectedFile);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setError('请先选择一张图片');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);

      // 调用后端API
      const response = await fetch('http://localhost:8000/api/v1/detection/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // 检测成功，跳转到结果页面
      router.push(`/detection/result?id=${data.detection_id}`);
    } catch (err) {
      console.error('上传失败:', err);
      setError('图片上传或检测失败，请稍后再试');
      setLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="w-full">
      <h2 className="text-2xl font-semibold text-center mb-6">上传图片进行AI篡改检测</h2>
      
      {error && (
        <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div 
          className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={triggerFileInput}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <input 
            type="file" 
            ref={fileInputRef}
            onChange={handleFileChange} 
            accept="image/*" 
            className="hidden" 
          />
          
          {preview ? (
            <div className="w-full">
              <img 
                src={preview} 
                alt="预览图" 
                className="max-h-80 mx-auto object-contain mb-4" 
              />
              <p className="text-center text-sm text-gray-500 mb-2">
                已选择: {file.name} ({(file.size / (1024 * 1024)).toFixed(2)} MB)
              </p>
              <p className="text-center text-sm text-blue-500">
                点击或拖拽更换图片
              </p>
            </div>
          ) : (
            <>
              <svg className="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <p className="text-center mb-2">
                <span className="text-blue-500 font-medium">点击上传</span> 或拖拽图片到此处
              </p>
              <p className="text-xs text-gray-500">
                支持JPG, PNG, WEBP等常见图片格式，最大10MB
              </p>
            </>
          )}
        </div>

        <div className="mt-6 flex justify-center">
          <button 
            type="submit" 
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg shadow-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={loading || !file}
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                检测中...
              </>
            ) : '开始检测'}
          </button>
        </div>
      </form>

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>免费用户每天可检测5张图片，<a href="#" className="text-blue-500 hover:underline">升级为付费用户</a>获取更多检测次数和高级功能</p>
      </div>
    </div>
  );
};

export default ImageUploader;
