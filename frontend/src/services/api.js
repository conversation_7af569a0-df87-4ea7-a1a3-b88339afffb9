import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 处理401错误 - 未授权
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 用户登录
  login: (email, password) => {
    const formData = new FormData();
    formData.append('username', email);
    formData.append('password', password);
    return api.post('/auth/token', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 用户注册
  register: (userData) => {
    return api.post('/auth/register', userData);
  },
  
  // 获取当前用户信息
  getCurrentUser: () => {
    return api.get('/auth/me');
  },
  
  // 修改密码
  changePassword: (oldPassword, newPassword) => {
    return api.post('/auth/change-password', { old_password: oldPassword, new_password: newPassword });
  },
  
  // 升级计划
  upgradePlan: (planType) => {
    return api.post('/auth/upgrade-plan', { plan_type: planType });
  }
};

// 用户相关API
export const userAPI = {
  // 获取用户资料
  getProfile: () => {
    return api.get('/users/profile');
  },
  
  // 更新用户资料
  updateProfile: (profileData) => {
    return api.put('/users/profile', profileData);
  },
  
  // 获取用户历史记录
  getHistory: (skip = 0, limit = 10) => {
    return api.get(`/users/history?skip=${skip}&limit=${limit}`);
  },
  
  // 删除历史记录
  deleteHistoryItem: (detectionId) => {
    return api.delete(`/users/history/${detectionId}`);
  },
  
  // 获取用户仪表盘数据
  getDashboard: () => {
    return api.get('/users/dashboard');
  },
  
  // 获取用户通知
  getNotifications: (skip = 0, limit = 10) => {
    return api.get(`/users/notifications?skip=${skip}&limit=${limit}`);
  },
  
  // 标记通知为已读
  markNotificationAsRead: (notificationId) => {
    return api.post(`/users/notifications/${notificationId}/read`);
  }
};

// 图片检测相关API
export const detectionAPI = {
  // 上传图片进行检测
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/detection/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 获取检测结果
  getDetectionResult: (detectionId) => {
    return api.get(`/detection/result/${detectionId}`);
  },
  
  // 获取检测历史
  getDetectionHistory: (skip = 0, limit = 10) => {
    return api.get(`/detection/history?skip=${skip}&limit=${limit}`);
  },
  
  // 删除检测结果
  deleteDetectionResult: (detectionId) => {
    return api.delete(`/detection/result/${detectionId}`);
  },
  
  // 批量检测（高级功能）
  batchDetection: (files) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    return api.post('/detection/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 获取检测统计信息
  getDetectionStats: () => {
    return api.get('/detection/stats');
  }
};

export default api;
