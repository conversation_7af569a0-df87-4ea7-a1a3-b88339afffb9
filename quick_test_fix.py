#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的API
"""

import requests
import json
import time

def test_fixed_api():
    """测试修复后的API"""
    print("🔧 测试修复后的API...")

    try:
        # 1. 检查后端健康状态
        print("1️⃣ 检查后端健康状态...")
        try:
            health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
            if health_response.status_code == 200:
                print("✅ 后端健康检查通过")
            else:
                print(f"❌ 后端健康检查失败: {health_response.status_code}")
                print(f"响应内容: {health_response.text}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到后端服务器")
            print("💡 请确保后端正在运行: cd backend && python3 main.py")
            return False
        
        # 2. 上传测试文件
        print("2️⃣ 上传测试文件...")
        test_content = b"test image content for API fix verification"
        files = {'file': ('test_fix.jpg', test_content, 'image/jpeg')}
        
        upload_response = requests.post(
            "http://localhost:8000/api/v1/detection/upload",
            files=files,
            timeout=30
        )
        
        if upload_response.status_code != 200:
            print(f"❌ 上传失败: {upload_response.status_code}")
            print(f"响应: {upload_response.text}")
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data['data']['file_id']
        print(f"✅ 上传成功，文件ID: {file_id}")
        
        # 3. 获取检测结果
        print("3️⃣ 获取检测结果...")
        time.sleep(1)  # 等待一下
        
        result_response = requests.get(
            f"http://localhost:8000/api/v1/detection/result/{file_id}",
            timeout=30
        )
        
        print(f"检测结果状态码: {result_response.status_code}")
        
        if result_response.status_code == 200:
            result_data = result_response.json()
            print("✅ 检测结果获取成功！")
            print("📊 检测结果:")
            print(json.dumps(result_data, indent=2, ensure_ascii=False))
        elif result_response.status_code == 500:
            print(f"❌ 500错误 - 服务器内部错误")
            print(f"错误详情: {result_response.text}")
            try:
                error_data = result_response.json()
                print(f"错误信息: {error_data.get('detail', '未知错误')}")
            except:
                pass
            return False
            
            # 4. 测试图片访问
            print("4️⃣ 测试图片访问...")
            image_response = requests.get(
                f"http://localhost:8000/api/v1/detection/image/{file_id}",
                timeout=10
            )
            print(f"原图访问状态码: {image_response.status_code}")
            
            # 5. 测试热力图访问
            heatmap_url = result_data['data'].get('heatmap_url')
            if heatmap_url:
                print("5️⃣ 测试热力图访问...")
                heatmap_response = requests.get(
                    f"http://localhost:8000{heatmap_url}",
                    timeout=10
                )
                print(f"热力图访问状态码: {heatmap_response.status_code}")
            
            return True
        else:
            print(f"❌ 检测结果获取失败: {result_response.status_code}")
            print(f"错误响应: {result_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    print("🚀 开始快速测试修复...")
    
    success = test_fixed_api()
    
    if success:
        print("\n🎉 API修复成功！")
        print("💡 现在可以在前端正常使用了")
        print("🌐 访问 http://localhost:3000 进行测试")
    else:
        print("\n❌ 仍有问题，请检查后端日志")
        print("💡 确保后端正在运行: cd backend && python3 main.py")

if __name__ == "__main__":
    main()
