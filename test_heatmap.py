#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热力图功能测试脚本
"""

import requests
import json
import os
import time
from datetime import datetime

def test_heatmap_generation():
    """测试热力图生成功能"""
    print("=" * 70)
    print("🔥 测试热力图生成功能")
    print("=" * 70)
    
    try:
        # 检查后端是否运行
        health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 后端服务器未运行")
            print("💡 请先启动后端: cd backend && python3 main.py")
            return False
        
        print("✅ 后端服务器运行正常")
        
        # 创建一个测试图片文件
        test_image_content = b"fake image content for testing"
        test_image_path = "test_heatmap_image.jpg"
        
        with open(test_image_path, 'wb') as f:
            f.write(test_image_content)
        
        print(f"📤 上传测试图片: {test_image_path}")
        
        # 上传图片
        with open(test_image_path, 'rb') as f:
            files = {'file': (test_image_path, f, 'image/jpeg')}
            upload_response = requests.post(
                "http://localhost:8000/api/v1/detection/upload",
                files=files,
                timeout=30
            )
        
        # 清理测试文件
        os.remove(test_image_path)
        
        if upload_response.status_code != 200:
            print(f"❌ 图片上传失败: {upload_response.status_code}")
            print(f"📄 响应: {upload_response.text}")
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data['data']['file_id']
        print(f"✅ 图片上传成功，ID: {file_id}")
        
        # 等待一下让检测完成
        print("⏳ 等待AI检测完成...")
        time.sleep(2)
        
        # 获取检测结果
        print("🔍 获取检测结果...")
        result_response = requests.get(
            f"http://localhost:8000/api/v1/detection/result/{file_id}",
            timeout=30
        )
        
        if result_response.status_code != 200:
            print(f"❌ 获取检测结果失败: {result_response.status_code}")
            print(f"📄 响应: {result_response.text}")
            return False
        
        result_data = result_response.json()
        print("✅ 检测完成！")
        print("📊 检测结果:")
        print(json.dumps(result_data, indent=2, ensure_ascii=False))
        
        # 检查是否有热力图
        heatmap_url = result_data['data'].get('heatmap_url')
        if heatmap_url:
            print(f"🔥 热力图URL: {heatmap_url}")
            
            # 尝试获取热力图
            print("📥 下载热力图...")
            heatmap_response = requests.get(
                f"http://localhost:8000{heatmap_url}",
                timeout=30
            )
            
            if heatmap_response.status_code == 200:
                print("✅ 热力图下载成功！")
                print(f"📏 热力图大小: {len(heatmap_response.content)} 字节")
                print(f"📋 内容类型: {heatmap_response.headers.get('content-type', 'unknown')}")
                
                # 保存热力图到本地查看
                heatmap_filename = f"downloaded_heatmap_{file_id}.png"
                with open(heatmap_filename, 'wb') as f:
                    f.write(heatmap_response.content)
                print(f"💾 热力图已保存为: {heatmap_filename}")
                
                return True
            else:
                print(f"❌ 热力图下载失败: {heatmap_response.status_code}")
                return False
        else:
            print("⚠️  没有生成热力图（可能检测结果为正常）")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_multiple_detections():
    """测试多次检测以确保热力图变化"""
    print("\n" + "=" * 70)
    print("🔄 测试多次检测")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\n🧪 测试 {i+1}/{total_tests}")
        
        # 创建不同的测试文件
        test_content = f"test image content {i} - {datetime.now()}".encode()
        test_filename = f"test_image_{i}.jpg"
        
        with open(test_filename, 'wb') as f:
            f.write(test_content)
        
        try:
            # 上传
            with open(test_filename, 'rb') as f:
                files = {'file': (test_filename, f, 'image/jpeg')}
                upload_response = requests.post(
                    "http://localhost:8000/api/v1/detection/upload",
                    files=files,
                    timeout=30
                )
            
            if upload_response.status_code == 200:
                file_id = upload_response.json()['data']['file_id']
                print(f"✅ 上传成功: {file_id}")
                
                # 获取结果
                time.sleep(1)
                result_response = requests.get(
                    f"http://localhost:8000/api/v1/detection/result/{file_id}",
                    timeout=30
                )
                
                if result_response.status_code == 200:
                    result = result_response.json()
                    is_fake = result['data'].get('is_fake', False)
                    confidence = result['data'].get('confidence', 0)
                    has_heatmap = result['data'].get('heatmap_url') is not None
                    
                    print(f"   检测结果: {'疑似篡改' if is_fake else '正常'}")
                    print(f"   置信度: {confidence:.2f}")
                    print(f"   热力图: {'有' if has_heatmap else '无'}")
                    
                    if has_heatmap:
                        success_count += 1
                else:
                    print(f"   ❌ 获取结果失败: {result_response.status_code}")
            else:
                print(f"   ❌ 上传失败: {upload_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")
        finally:
            # 清理测试文件
            if os.path.exists(test_filename):
                os.remove(test_filename)
    
    print(f"\n📊 测试总结: {success_count}/{total_tests} 次生成了热力图")
    return success_count > 0

def main():
    """主函数"""
    print("🚀 开始热力图功能测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础热力图测试
    basic_success = test_heatmap_generation()
    
    # 多次检测测试
    multiple_success = test_multiple_detections()
    
    # 总结
    print("\n" + "=" * 70)
    print("📋 热力图测试总结")
    print("=" * 70)
    print(f"{'✅' if basic_success else '❌'} 基础热力图功能: {'正常' if basic_success else '异常'}")
    print(f"{'✅' if multiple_success else '❌'} 多次检测测试: {'正常' if multiple_success else '异常'}")
    
    if basic_success and multiple_success:
        print("\n🎉 热力图功能测试通过！")
        print("💡 现在你可以在前端看到带有红色标记的热力图了")
        print("🌟 热力图会显示AI检测到的可疑篡改区域")
    else:
        print("\n⚠️  部分测试失败，请检查后端配置")
        print("💡 确保后端服务器正在运行: cd backend && python3 main.py")

if __name__ == "__main__":
    main()
