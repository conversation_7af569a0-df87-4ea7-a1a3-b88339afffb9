#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure API集成测试脚本
测试慧眼AI篡改图片检测平台与Azure Content Safety API的集成
"""

import requests
import json
import base64
import os
from datetime import datetime

# Azure配置
AZURE_API_KEY = "7Mt2w73ZVyC3Ylqw9RZLWb5yI9I1OWtWKBAE1hd2t8aQxfrMIrcWJQQJ99BIACNns7RXJ3w3AAAHACOGDtyU"
AZURE_ENDPOINT = "https://aegisdetectorservice.cognitiveservices.azure.com"

def test_azure_direct():
    """直接测试Azure Content Safety API"""
    print("=" * 70)
    print("🔗 直接测试Azure Content Safety API")
    print("=" * 70)
    
    try:
        # 创建一个简单的测试图片（1x1像素的透明PNG）
        tiny_png_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        # 构建请求
        headers = {
            "Content-Type": "application/json",
            "Ocp-Apim-Subscription-Key": AZURE_API_KEY
        }
        
        api_url = f"{AZURE_ENDPOINT}/contentsafety/image:analyze?api-version=2023-10-01"
        
        body = {
            "image": {
                "content": tiny_png_base64
            }
        }
        
        print(f"✅ API终结点: {AZURE_ENDPOINT}")
        print(f"✅ API密钥: {'*' * 60}{AZURE_API_KEY[-8:]}")
        print(f"✅ 请求URL: {api_url}")
        print(f"🚀 正在发送请求...")
        
        response = requests.post(api_url, headers=headers, json=body, timeout=30)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Azure API调用成功！")
            print("📊 分析结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ Azure API调用失败")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_backend_integration():
    """测试后端集成"""
    print("\n" + "=" * 70)
    print("🔧 测试后端Azure集成")
    print("=" * 70)
    
    try:
        # 检查后端是否运行
        health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 后端服务器未运行")
            print("💡 请先启动后端: cd backend && python3 main.py")
            return False
        
        print("✅ 后端服务器运行正常")
        
        # 测试文件上传和检测
        test_file = "README.md"
        if not os.path.exists(test_file):
            print(f"❌ 测试文件 {test_file} 不存在")
            return False
        
        print(f"📤 上传测试文件: {test_file}")
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            upload_response = requests.post(
                "http://localhost:8000/api/v1/detection/upload",
                files=files,
                timeout=30
            )
        
        if upload_response.status_code == 200:
            upload_data = upload_response.json()
            file_id = upload_data['data']['file_id']
            print(f"✅ 文件上传成功，ID: {file_id}")
            
            # 获取检测结果
            print("🔍 获取检测结果...")
            result_response = requests.get(
                f"http://localhost:8000/api/v1/detection/result/{file_id}",
                timeout=30
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                print("✅ 检测完成！")
                print("📊 检测结果:")
                print(json.dumps(result_data, indent=2, ensure_ascii=False))
                return True
            else:
                print(f"❌ 获取检测结果失败: {result_response.status_code}")
                print(f"📄 响应: {result_response.text}")
                return False
        else:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            print(f"📄 响应: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 后端集成测试失败: {str(e)}")
        return False

def test_frontend_backend_flow():
    """测试前端到后端的完整流程"""
    print("\n" + "=" * 70)
    print("🌐 测试前端到后端完整流程")
    print("=" * 70)
    
    try:
        # 模拟前端请求
        print("🎭 模拟前端上传请求...")
        
        # 创建一个测试图片文件
        test_image_path = "test_image.txt"
        with open(test_image_path, 'w') as f:
            f.write("This is a test image content")
        
        # 模拟前端上传
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_image.jpg', f, 'image/jpeg')}
            headers = {
                'Origin': 'http://localhost:3000',  # 模拟前端域名
            }
            
            upload_response = requests.post(
                "http://localhost:8000/api/v1/detection/upload",
                files=files,
                headers=headers,
                timeout=30
            )
        
        # 清理测试文件
        os.remove(test_image_path)
        
        if upload_response.status_code == 200:
            print("✅ 前端上传模拟成功")
            
            # 检查CORS头
            cors_headers = {k: v for k, v in upload_response.headers.items() 
                          if k.lower().startswith('access-control')}
            if cors_headers:
                print("🌐 CORS配置正确:")
                for header, value in cors_headers.items():
                    print(f"   {header}: {value}")
            
            return True
        else:
            print(f"❌ 前端上传模拟失败: {upload_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端流程测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始Azure API集成测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试Azure API直连
    azure_success = test_azure_direct()
    
    # 测试后端集成
    backend_success = test_backend_integration()
    
    # 测试前端流程
    frontend_success = test_frontend_backend_flow()
    
    # 总结
    print("\n" + "=" * 70)
    print("📋 集成测试总结")
    print("=" * 70)
    print(f"{'✅' if azure_success else '❌'} Azure API直连: {'成功' if azure_success else '失败'}")
    print(f"{'✅' if backend_success else '❌'} 后端集成: {'成功' if backend_success else '失败'}")
    print(f"{'✅' if frontend_success else '❌'} 前端流程: {'成功' if frontend_success else '失败'}")
    
    if azure_success and backend_success and frontend_success:
        print("\n🎉 所有集成测试通过！")
        print("💡 你的慧眼AI检测平台已经完全配置好了")
        print("🌟 现在可以启动前端进行完整测试: cd frontend && npm run dev")
    elif azure_success:
        print("\n✅ Azure API配置正确")
        if not backend_success:
            print("⚠️  请检查后端服务器是否正在运行")
        if not frontend_success:
            print("⚠️  请检查CORS配置")
    else:
        print("\n❌ Azure API配置有问题，请检查密钥和终结点")

if __name__ == "__main__":
    main()
