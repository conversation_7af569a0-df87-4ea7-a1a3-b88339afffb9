#!/bin/bash

# 设置颜色输出
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${GREEN}===== 启动慧眼AI篡改图片检测平台 =====${NC}"

# 检查是否安装了必要的依赖
echo -e "${BLUE}检查依赖...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${YELLOW}未找到Python3，请安装Python3后再运行此脚本${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${YELLOW}未找到npm，请安装Node.js和npm后再运行此脚本${NC}"
    exit 1
fi

# 启动后端服务
echo -e "${BLUE}启动后端服务...${NC}"
cd backend

# 检查是否存在虚拟环境，如果不存在则创建
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}创建Python虚拟环境...${NC}"
    python3 -m venv venv
fi

# 激活虚拟环境
echo -e "${BLUE}激活虚拟环境...${NC}"
source venv/bin/activate

# 安装依赖
echo -e "${BLUE}安装后端依赖...${NC}"
pip install -r requirements.txt

# 启动后端服务（后台运行）
echo -e "${GREEN}启动后端服务...${NC}"
python main.py &
BACKEND_PID=$!
echo -e "${GREEN}后端服务已启动，PID: ${BACKEND_PID}${NC}"

# 返回项目根目录
cd ..

# 启动前端服务
echo -e "${BLUE}启动前端服务...${NC}"
cd frontend

# 安装依赖
echo -e "${BLUE}安装前端依赖...${NC}"
npm install

# 启动前端开发服务器
echo -e "${GREEN}启动前端开发服务器...${NC}"
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}前端服务已启动，PID: ${FRONTEND_PID}${NC}"

# 返回项目根目录
cd ..

echo -e "${GREEN}===== 服务已启动 =====${NC}"
echo -e "${BLUE}后端API地址: ${YELLOW}http://localhost:8000${NC}"
echo -e "${BLUE}前端页面地址: ${YELLOW}http://localhost:3000${NC}"
echo -e "${BLUE}按Ctrl+C停止服务${NC}"

# 等待用户按Ctrl+C
trap "echo -e '${YELLOW}正在停止服务...${NC}'; kill $BACKEND_PID $FRONTEND_PID; echo -e '${GREEN}服务已停止${NC}'; exit" INT
wait
