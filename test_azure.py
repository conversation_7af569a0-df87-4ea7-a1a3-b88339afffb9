import json
import requests

# 1. 将您自己的密钥和终结点粘贴到这里
# 确保不要在终结点后面添加任何额外的斜杠
key = "7Mt2w73ZVyC3Ylqw9RZLWb5yI9I1OWtWKBAE1hd2t8aQxfrMIrcWJQQJ99BIACNns7RXJ3w3AAAHACOGDtyU"
endpoint = "https://aegisdetectorservice.cognitiveservices.azure.com"
# 2. 准备API请求
# API的完整URL
api_url = f"{endpoint}/contentsafety/image:analyze?api-version=2023-10-01"

# 设置请求头，这是身份验证的方式
headers = {
    "Ocp-Apim-Subscription-Key": key,
    "Content-Type": "application/json"
}

# 3. 指定要分析的图片
# 您可以使用任何公开可访问的图片URL
image_url_to_analyze = "https://www.ikea.com/ca/en/images/products/fejka-artificial-potted-plant-with-pot-in-outdoor-succulent__0614212_pe686835_s5.jpg"

# 构建请求体 (Body)
body = {
    "image": {
        "url": image_url_to_analyze
    },
    # 您可以指定要分析的类别
    "categories": ["Hate", "SelfHarm", "Sexual", "Violence"]
}

# 4. 检查配置并发送请求
print("=" * 60)
print("Azure Content Safety API 测试")
print("=" * 60)

# 检查配置
if key == "PASTE_YOUR_KEY_HERE" or not key:
    print("❌ 错误: API密钥未配置")
    print("请在代码中设置正确的Azure API密钥")
    exit(1)

if endpoint == "PASTE_YOUR_ENDPOINT_HERE" or not endpoint:
    print("❌ 错误: API终结点未配置")
    print("请在代码中设置正确的Azure API终结点")
    exit(1)

print(f"✅ API终结点: {endpoint}")
print(f"✅ API密钥: {'*' * (len(key) - 8) + key[-8:] if len(key) > 8 else '***'}")
print(f"✅ 分析图片: {image_url_to_analyze}")
print(f"✅ 请求URL: {api_url}")

try:
    print(f"\n🚀 正在分析图片...")
    response = requests.post(api_url, headers=headers, json=body, timeout=30)

    # 检查响应状态码
    print(f"📡 响应状态码: {response.status_code}")

    if response.status_code == 200:
        # 5. 解析并打印结果
        result = response.json()
        print("\n✅ --- 分析结果 ---")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

except requests.exceptions.Timeout:
    print("❌ 请求超时，请检查网络连接")
except requests.exceptions.ConnectionError:
    print("❌ 连接错误，请检查网络连接和API终结点")
except requests.exceptions.RequestException as e:
    print(f"❌ 请求失败: {e}")
except Exception as e:
    print(f"❌ 发生未知错误: {e}")

print("\n" + "=" * 60)
print("测试完成")
print("=" * 60)
