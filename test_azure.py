import json
import requests

# 1. 将您自己的密钥和终结点粘贴到这里
# 确保不要在终结点后面添加任何额外的斜杠
key = "PASTE_YOUR_KEY_HERE"  # 7Mt2w73ZVyC3Ylqw9RZLWb5yI9I1OWtWKBAE1hd2t8aQxfrMIrcWJQQJ99BIACNns7RXJ3w3AAAHACOGDtyU
endpoint = "PASTE_YOUR_ENDPOINT_HERE" # https://aegisdetectorservice.cognitiveservices.azure.com/
# 2. 准备API请求
# API的完整URL
api_url = f"{endpoint}/contentsafety/image:analyze?api-version=2023-10-01"

# 设置请求头，这是身份验证的方式
headers = {
    "Ocp-Apim-Subscription-Key": key,
    "Content-Type": "application/json"
}

# 3. 指定要分析的图片
# 您可以使用任何公开可访问的图片URL
image_url_to_analyze = "https://www.ikea.com/ca/en/images/products/fejka-artificial-potted-plant-with-pot-in-outdoor-succulent__0614212_pe686835_s5.jpg"

# 构建请求体 (Body)
body = {
    "image": {
        "url": image_url_to_analyze
    },
    # 您可以指定要分析的类别
    "categories": ["Hate", "SelfHarm", "Sexual", "Violence"]
}

# 4. 发送请求并获取响应
try:
    print(f"正在分析图片: {image_url_to_analyze}")
    response = requests.post(api_url, headers=headers, json=body)

    # 检查响应状态码
    response.raise_for_status()  # 如果请求失败 (例如401, 404), 会抛出异常

    # 5. 解析并打印结果
    result = response.json()
    print("\n--- 分析结果 ---")
    # 使用json.dumps美化输出
    print(json.dumps(result, indent=2))

except requests.exceptions.RequestException as e:
    print(f"\n请求失败: {e}")
except Exception as e:
    print(f"\n发生错误: {e}")
