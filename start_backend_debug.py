#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模式启动后端服务器
"""

import os
import sys
import subprocess
import time

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 确保uploads目录存在
    uploads_dir = os.path.join(os.getcwd(), "uploads")
    os.makedirs(uploads_dir, exist_ok=True)
    print(f"✅ 创建uploads目录: {uploads_dir}")
    
    # 创建子目录
    for subdir in ["temp", "heatmaps"]:
        subdir_path = os.path.join(uploads_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        print(f"✅ 创建子目录: {subdir_path}")

def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "python-multipart",
        "aiofiles",
        "requests",
        "pydantic"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def start_backend():
    """启动后端"""
    print("🚀 启动后端服务器...")
    
    # 切换到backend目录
    backend_dir = os.path.join(os.getcwd(), "backend")
    if not os.path.exists(backend_dir):
        print("❌ backend目录不存在")
        return False
    
    os.chdir(backend_dir)
    print(f"📁 切换到目录: {backend_dir}")
    
    # 启动服务器
    try:
        print("🔄 启动uvicorn服务器...")
        print("📝 如果出现错误，请查看下面的详细信息")
        print("-" * 50)
        
        # 使用subprocess启动，这样可以看到详细的错误信息
        result = subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], capture_output=False, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False

def main():
    print("🎯 调试模式启动后端")
    print("=" * 50)
    
    # 1. 设置环境
    setup_environment()
    
    # 2. 检查依赖
    if not check_dependencies():
        return
    
    # 3. 启动后端
    start_backend()

if __name__ == "__main__":
    main()
