#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端问题调试脚本
"""

import requests
import json
import time
from datetime import datetime

def check_backend_status():
    """检查后端状态"""
    print("=" * 60)
    print("🔍 检查后端状态")
    print("=" * 60)
    
    try:
        # 检查根路径
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端根路径正常")
            print(f"📡 响应: {response.json()}")
        else:
            print(f"❌ 后端根路径异常: {response.status_code}")
            return False
            
        # 检查健康检查
        health_response = requests.get("http://localhost:8000/api/v1/detection/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ 检测服务正常")
            print(f"📊 健康状态: {health_response.json()}")
        else:
            print(f"❌ 检测服务异常: {health_response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        print("💡 请确保后端正在运行: cd backend && python3 main.py")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def simulate_frontend_flow():
    """模拟前端完整流程"""
    print("\n" + "=" * 60)
    print("🎭 模拟前端完整流程")
    print("=" * 60)
    
    try:
        # 步骤1: 上传文件
        print("📤 步骤1: 上传测试文件...")
        
        test_content = b"test image content for debugging"
        files = {'file': ('test_debug.jpg', test_content, 'image/jpeg')}
        
        upload_response = requests.post(
            "http://localhost:8000/api/v1/detection/upload",
            files=files,
            timeout=30
        )
        
        if upload_response.status_code != 200:
            print(f"❌ 上传失败: {upload_response.status_code}")
            print(f"📄 响应: {upload_response.text}")
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data['data']['file_id']
        print(f"✅ 上传成功，文件ID: {file_id}")
        print(f"📊 上传响应: {json.dumps(upload_data, indent=2, ensure_ascii=False)}")
        
        # 步骤2: 获取检测结果
        print(f"\n🔍 步骤2: 获取检测结果 (ID: {file_id})...")
        
        result_response = requests.get(
            f"http://localhost:8000/api/v1/detection/result/{file_id}",
            timeout=30
        )
        
        print(f"📡 检测结果状态码: {result_response.status_code}")
        
        if result_response.status_code == 200:
            result_data = result_response.json()
            print("✅ 获取检测结果成功")
            print(f"📊 检测结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
            
            # 步骤3: 检查图片访问
            print(f"\n🖼️ 步骤3: 检查原图访问...")
            image_response = requests.get(
                f"http://localhost:8000/api/v1/detection/image/{file_id}",
                timeout=10
            )
            print(f"📡 原图访问状态码: {image_response.status_code}")
            
            # 步骤4: 检查热力图访问
            heatmap_url = result_data['data'].get('heatmap_url')
            if heatmap_url:
                print(f"\n🔥 步骤4: 检查热力图访问...")
                heatmap_response = requests.get(
                    f"http://localhost:8000{heatmap_url}",
                    timeout=10
                )
                print(f"📡 热力图访问状态码: {heatmap_response.status_code}")
                
                if heatmap_response.status_code == 200:
                    print(f"✅ 热力图访问成功，大小: {len(heatmap_response.content)} 字节")
                else:
                    print(f"❌ 热力图访问失败: {heatmap_response.text}")
            else:
                print("ℹ️  没有热力图URL")
            
            return True
        else:
            print(f"❌ 获取检测结果失败: {result_response.status_code}")
            print(f"📄 错误响应: {result_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 模拟流程失败: {str(e)}")
        return False

def check_cors_headers():
    """检查CORS头信息"""
    print("\n" + "=" * 60)
    print("🌐 检查CORS配置")
    print("=" * 60)
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/detection/health",
            headers={'Origin': 'http://localhost:3000'},
            timeout=5
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        cors_headers = {k: v for k, v in response.headers.items() 
                       if k.lower().startswith('access-control')}
        
        if cors_headers:
            print("✅ CORS头信息:")
            for header, value in cors_headers.items():
                print(f"   {header}: {value}")
        else:
            print("⚠️  没有找到CORS头信息")
            
        return True
        
    except Exception as e:
        print(f"❌ CORS检查失败: {str(e)}")
        return False

def check_frontend_api_calls():
    """检查前端API调用格式"""
    print("\n" + "=" * 60)
    print("🔧 检查前端API调用格式")
    print("=" * 60)
    
    # 模拟前端的API调用
    test_cases = [
        {
            "name": "检测结果API",
            "url": "http://localhost:8000/api/v1/detection/result/test-id",
            "expected_status": 404  # 因为test-id不存在
        },
        {
            "name": "图片访问API", 
            "url": "http://localhost:8000/api/v1/detection/image/test-id",
            "expected_status": 404
        },
        {
            "name": "热力图API",
            "url": "http://localhost:8000/api/v1/detection/heatmap/test-id", 
            "expected_status": 404
        }
    ]
    
    for test_case in test_cases:
        try:
            response = requests.get(test_case["url"], timeout=5)
            print(f"📡 {test_case['name']}: {response.status_code}")
            
            if response.status_code == test_case["expected_status"]:
                print(f"✅ {test_case['name']} 端点存在")
            else:
                print(f"⚠️  {test_case['name']} 状态码异常")
                
        except Exception as e:
            print(f"❌ {test_case['name']} 测试失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 开始前端问题调试...")
    print(f"⏰ 调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查后端状态
    backend_ok = check_backend_status()
    
    if not backend_ok:
        print("\n❌ 后端服务器有问题，请先解决后端问题")
        return
    
    # 模拟前端流程
    flow_ok = simulate_frontend_flow()
    
    # 检查CORS
    cors_ok = check_cors_headers()
    
    # 检查API端点
    check_frontend_api_calls()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 调试总结")
    print("=" * 60)
    print(f"{'✅' if backend_ok else '❌'} 后端状态: {'正常' if backend_ok else '异常'}")
    print(f"{'✅' if flow_ok else '❌'} 完整流程: {'正常' if flow_ok else '异常'}")
    print(f"{'✅' if cors_ok else '❌'} CORS配置: {'正常' if cors_ok else '异常'}")
    
    if backend_ok and flow_ok and cors_ok:
        print("\n🎉 后端功能正常！")
        print("💡 前端错误可能是:")
        print("   1. 网络连接问题")
        print("   2. 前端代码中的URL错误")
        print("   3. 浏览器缓存问题")
        print("\n🔧 建议解决方案:")
        print("   1. 清除浏览器缓存")
        print("   2. 检查浏览器开发者工具的Network标签")
        print("   3. 确保前端和后端都在运行")
    else:
        print("\n⚠️  发现问题，请根据上述信息进行修复")

if __name__ == "__main__":
    main()
