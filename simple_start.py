#!/usr/bin/env python3
import os
import subprocess
import sys

print("🔧 简单启动脚本")
print("================")

# 创建目录
print("📁 创建必要目录...")
os.makedirs("uploads/temp", exist_ok=True)
os.makedirs("uploads/heatmaps", exist_ok=True)
os.makedirs("uploads/images", exist_ok=True)
print("✅ 目录创建完成")

# 安装依赖
print("📦 安装基础依赖...")
packages = ["fastapi", "uvicorn", "python-multipart", "aiofiles", "requests", "pydantic"]
for package in packages:
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", package], check=True, capture_output=True)
        print(f"✅ {package}")
    except:
        print(f"❌ {package} 安装失败")

# 启动后端
print("🚀 启动后端...")
os.chdir("backend")
subprocess.run([sys.executable, "main.py"])
